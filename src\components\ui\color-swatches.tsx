"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import type { CanvasBackgroundColor } from "@/lib/types";

const swatches = [
  // Grayscale
  "#ffffff", "#f2f2f2", "#e6e6e6", "#d9d9d9", "#cccccc", "#bfbfbf", "#b3b3b3", "#a6a6a6", "#999999", "#8c8c8c", "#808080", "#737373", "#666666", "#595959", "#4d4d4d", "#404040", "#333333", "#262626", "#1a1a1a", "#0d0d0d", "#000000",
  // Reds
  "#ff0000", "#e60000", "#cc0000", "#b30000", "#990000", "#800000", "#660000",
  // Oranges
  "#ff8000", "#e67300", "#cc6600", "#b35900", "#994d00", "#804000", "#663300",
  // Yellows
  "#ffff00", "#e6e600", "#cccc00", "#b3b300", "#999900", "#808000", "#666600",
  // Greens
  "#00ff00", "#00e600", "#00cc00", "#00b300", "#009900", "#008000", "#006600",
  // Cyans
  "#00ffff", "#00e6e6", "#00cccc", "#00b3b3", "#009999", "#008080", "#006666",
  // Blues
  "#0000ff", "#0000e6", "#0000cc", "#0000b3", "#000099", "#000080", "#000066",
  // Violets
  "#8000ff", "#7300e6", "#6600cc", "#5900b3", "#4d0099", "#400080", "#330066",
];

export interface ColorSwatchesProps {
  value: CanvasBackgroundColor;
  onChange: (value: CanvasBackgroundColor) => void;
  className?: string;
}

const ColorSwatches = React.forwardRef<HTMLDivElement, ColorSwatchesProps>(
  ({ value, onChange, className }, ref) => {
    return (
      <div ref={ref} className={cn("grid grid-cols-11 gap-1", className)}>
        {swatches.map((color) => (
          <button
            key={color}
            type="button"
            className={cn(
              "h-6 w-6 rounded-sm border transition-all",
              value.toLowerCase() === color.toLowerCase()
                ? "border-ring ring-2 ring-ring ring-offset-2"
                : "border-input"
            )}
            style={{ backgroundColor: color }}
            onClick={() => onChange(color)}
            aria-label={`Select ${color}`}
          />
        ))}
      </div>
    );
  }
);

ColorSwatches.displayName = "ColorSwatches";

export { ColorSwatches };