"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Settings } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ColorSwatches } from "@/components/ui/color-swatches";
import type { CanvasBackgroundColor } from "@/lib/types";
import FileUpload from "@/components/ui/file-upload";

interface GeneralSettingsPanelProps {
  canvasBackgroundColor: CanvasBackgroundColor;
  onCanvasBackgroundColorChange: (color: CanvasBackgroundColor) => void;
  onBackgroundImageUpload: (file: File, dataUri: string) => void;
}

export default function GeneralSettingsPanel({
  canvasBackgroundColor,
  onCanvasBackgroundColorChange,
  onBackgroundImageUpload,
}: GeneralSettingsPanelProps) {
  return (
    <Card className="h-full overflow-y-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="mr-2 h-6 w-6 text-accent" />
          General Settings
        </CardTitle>
        <CardDescription>Configure project-wide settings.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="project-name">Project Name</Label>
          <Input id="project-name" defaultValue="My Awesome Music Video" />
        </div>
        <div className="space-y-2">
          <Label htmlFor="aspect-ratio">Aspect Ratio</Label>
          <Select defaultValue="16:9">
            <SelectTrigger id="aspect-ratio">
              <SelectValue placeholder="Select aspect ratio" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="16:9">16:9 (Widescreen)</SelectItem>
              <SelectItem value="9:16">9:16 (Vertical)</SelectItem>
              <SelectItem value="1:1">1:1 (Square)</SelectItem>
              <SelectItem value="4:3">4:3 (Standard)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="resolution">Resolution</Label>
          <Select defaultValue="1080p">
            <SelectTrigger id="resolution">
              <SelectValue placeholder="Select resolution" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="720p">720p (HD)</SelectItem>
              <SelectItem value="1080p">1080p (Full HD)</SelectItem>
              <SelectItem value="4k">4K (Ultra HD)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>Canvas Background</Label>
          <ColorSwatches
            value={canvasBackgroundColor}
            onChange={onCanvasBackgroundColorChange}
          />
        </div>
        <div className="space-y-2">
          <Label>Background Image</Label>
          <FileUpload
            onFileUpload={onBackgroundImageUpload}
            accept={{ "image/*": [] }}
          />
        </div>
        <p className="text-sm text-muted-foreground pt-4">
          More settings like frame rate, etc., will be available here. (Coming Soon)
        </p>
      </CardContent>
    </Card>
  );
}
