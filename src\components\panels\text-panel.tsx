"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Type } from "lucide-react";

export default function TextPanel() {
  return (
    <Card className="h-full overflow-y-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Type className="mr-2 h-6 w-6 text-accent" />
          Text & Typography
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          Add and customize text overlays, lyrics, and titles. (Coming Soon)
        </p>
      </CardContent>
    </Card>
  );
}
