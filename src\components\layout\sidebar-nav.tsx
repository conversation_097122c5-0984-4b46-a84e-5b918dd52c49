"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import type { PanelKey } from "@/lib/types";
import { Settings, Film, Wand2, Waves, Type, Shapes, LucideIcon } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";

interface SidebarNavProps {
  activePanel: PanelKey;
  onSelectPanel: (panel: PanelKey) => void;
  isPanelOpen: boolean;
}

interface NavItem {
  key: PanelKey;
  label: string;
  icon: LucideIcon;
  description: string;
}

const navItems: NavItem[] = [
  { 
    key: "general", 
    label: "Settings", 
    icon: Settings,
    description: "Basic settings and configuration"
  },
  { 
    key: "media", 
    label: "Media", 
    icon: Film,
    description: "Upload and manage your media files"
  },
  { 
    key: "presets", 
    label: "Presets", 
    icon: Wand2,
    description: "Choose visual effects and styles"
  },
  { 
    key: "visualizer", 
    label: "Visualizer", 
    icon: Waves,
    description: "Customize audio visualization"
  },
  { 
    key: "text", 
    label: "Text", 
    icon: Type,
    description: "Add and style text overlays"
  },
  { 
    key: "elements", 
    label: "Elements", 
    icon: Shapes,
    description: "Add visual elements and shapes"
  },
];

export default function SidebarNav({ activePanel, onSelectPanel, isPanelOpen }: SidebarNavProps) {
  return (
    <nav className="flex flex-col py-2">
      {navItems.map((item) => (
        <Tooltip key={item.key} delayDuration={0}>
          <TooltipTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "w-16 h-16 rounded-none flex flex-col items-center justify-center gap-1 transition-all duration-200",
                activePanel === item.key && isPanelOpen && "bg-primary/10 border-l-2 border-primary",
                activePanel === item.key && !isPanelOpen && "bg-muted hover:bg-primary/10"
              )}
              onClick={() => onSelectPanel(item.key)}
            >
              <item.icon className={cn(
                "h-5 w-5",
                activePanel === item.key ? "text-primary" : "text-muted-foreground"
              )} />
              <span className="text-[10px] font-medium">
                {item.label}
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="right" className="text-sm">
            {item.description}
          </TooltipContent>
        </Tooltip>
      ))}
    </nav>
  );
}


