import { NextRequest } from 'next/server';
import { mkdir, writeFile, unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';
import os from 'os';

const execAsync = promisify(exec);

// Ensure temp directory exists
async function ensureTempDir() {
  const tempDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports');
  if (!existsSync(tempDir)) {
    await mkdir(tempDir, { recursive: true });
  }
  return tempDir;
}

// Clean up job directory
async function cleanupJobDir(jobDir: string) {
  try {
    // Remove all files in the job directory
    const { stdout, stderr } = await execAsync(`rm -rf "${jobDir}"`);
    console.log(`Cleaned up job directory: ${jobDir}`);
  } catch (error) {
    console.error(`Error cleaning up job directory: ${error}`);
  }
}

export async function POST(request: NextRequest) {
  try {
    // Create a unique ID for this export job
    const jobId = uuidv4();
    const tempDir = await ensureTempDir();
    const jobDir = path.join(tempDir, jobId);
    await mkdir(jobDir, { recursive: true });
    
    // Get the request data
    const formData = await request.formData();
    
    // Get frame count and export parameters
    const frameCount = parseInt(formData.get('frameCount') as string, 10);
    const fps = parseInt(formData.get('fps') as string, 10);
    const width = parseInt(formData.get('width') as string, 10);
    const height = parseInt(formData.get('height') as string, 10);
    const useHighQuality = formData.get('useHighQuality') === 'true';
    
    console.log(`Received ${frameCount} frames for processing. FPS: ${fps}, Resolution: ${width}x${height}`);
    
    // Get audio data
    const audioFile = formData.get('audio') as File;
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
    const audioPath = path.join(jobDir, 'audio.mp3');
    await writeFile(audioPath, audioBuffer);
    
    console.log(`Audio file saved: ${audioPath}`);
    
    // Process frames in batches to reduce memory usage
    const batchSize = 100;
    const numBatches = Math.ceil(frameCount / batchSize);
    
    for (let batchIndex = 0; batchIndex < numBatches; batchIndex++) {
      const batchStartFrame = batchIndex * batchSize;
      const batchEndFrame = Math.min((batchIndex + 1) * batchSize, frameCount);
      
      console.log(`Processing batch ${batchIndex + 1}/${numBatches} (frames ${batchStartFrame + 1}-${batchEndFrame})`);
      
      // Process each frame in the current batch
      const framePromises = [];
      
      for (let i = batchStartFrame; i < batchEndFrame; i++) {
        const frame = formData.get(`frame_${i}`) as string;
        if (!frame) {
          console.warn(`Frame ${i} not found in form data`);
          continue;
        }
        
        framePromises.push((async (index) => {
          const frameData = frame.replace(/^data:image\/\w+;base64,/, '');
          const frameBuffer = Buffer.from(frameData, 'base64');
          const framePath = path.join(jobDir, `frame_${index.toString().padStart(6, '0')}.jpg`);
          await writeFile(framePath, frameBuffer);
          return framePath;
        })(i));
      }
      
      // Wait for all frames in this batch to be written
      await Promise.all(framePromises);
      
      // Log progress
      console.log(`Batch ${batchIndex + 1}/${numBatches} processed`);
    }
    
    // Generate FFmpeg command with appropriate quality settings
    let ffmpegCommand = `ffmpeg -y -framerate ${fps} -i "${jobDir}/frame_%06d.jpg" -i "${audioPath}"`;
    
    if (useHighQuality) {
      // High quality settings
      ffmpegCommand += ` -c:v libx264 -preset slow -crf 18 -pix_fmt yuv420p -c:a aac -b:a 192k`;
    } else {
      // Standard quality settings
      ffmpegCommand += ` -c:v libx264 -preset medium -crf 23 -pix_fmt yuv420p -c:a aac -b:a 128k`;
    }
    
    // Add output path
    const outputPath = path.join(jobDir, 'output.mp4');
    ffmpegCommand += ` -shortest "${outputPath}"`;
    
    console.log(`Running FFmpeg command: ${ffmpegCommand}`);
    
    // Execute FFmpeg command
    await execAsync(ffmpegCommand);
    
    console.log(`Video created: ${outputPath}`);
    
    // Read the output file
    const outputFile = await Bun.file(outputPath).arrayBuffer();
    
    // Schedule cleanup after response is sent
    setTimeout(() => cleanupJobDir(jobDir), 1000);
    
    // Return the video file
    return new Response(outputFile, {
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Disposition': 'attachment; filename="rhythmic-canvas-video.mp4"'
      }
    });
  } catch (error) {
    console.error('Error processing video:', error);
    return new Response(`Error processing video: ${error}`, { status: 500 });
  }
}
