"use client";

import { useState, useRef, useEffect } from "react";
import { analyzeBeat, type AnalyzeBeatInput, type AnalyzeBeatOutput } from "@/ai/flows/analyze-beat";
import type { Beat<PERSON>ata, PanelKey, CanvasBackgroundColor } from "@/lib/types";
import { useToast } from "@/hooks/use-toast";

import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Play, Pause, RotateCcw, Music2, ChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";

// Import components
import SidebarNav from "@/components/layout/sidebar-nav";
import GeneralSettingsPanel from "@/components/panels/general-settings-panel";
import MediaPanel from "@/components/panels/media-panel";
import PresetsPanel from "@/components/panels/presets-panel";
import VisualizerPanel from "@/components/panels/visualizer-panel";
import TextPanel from "@/components/panels/text-panel";
import ElementsPanel from "@/components/panels/elements-panel";
import PreviewPlayer from "@/components/core/preview-player";
import ExportButton from "@/components/core/export-button";

const AppLogo = () => (
  <div className="flex items-center justify-center px-4 py-4">
    <div className="relative">
      <Music2 className="h-8 w-8 text-primary" />
      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-accent rounded-full flex items-center justify-center">
        <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
      </div>
    </div>
  </div>
);

export default function RhythmicCanvasApp() {
  const [activePanel, setActivePanel] = useState<PanelKey>("media");
  const [isPanelOpen, setIsPanelOpen] = useState(true);

  const [songFile, setSongFile] = useState<File | null>(null);
  const [songDataUri, setSongDataUri] = useState<string | null>(null);
  const [songFileName, setSongFileName] = useState<string | null>(null);

  const [visualMediaFile, setVisualMediaFile] = useState<File | null>(null);
  const [visualMediaSrc, setVisualMediaSrc] = useState<string | null>(null);
  const [visualMediaType, setVisualMediaType] = useState<"image" | "video" | null>(null);
  const [visualMediaFileName, setVisualMediaFileName] = useState<string | null>(null);

  const [beatData, setBeatData] = useState<BeatData | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [selectedPreset, setSelectedPreset] = useState<string | null>("pulse");
  const [pulseIntensity, setPulseIntensity] = useState<'light' | 'medium' | 'heavy'>('heavy');
  const [glitchIntensity, setGlitchIntensity] = useState<'light' | 'medium' | 'heavy'>('heavy');
  const [canvasBackgroundColor, setCanvasBackgroundColor] = useState<CanvasBackgroundColor>('#262626');
  const [backgroundImage, setBackgroundImage] = useState<string | null>(null);

  const { toast } = useToast();
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioProgress, setAudioProgress] = useState(0);
  const [audioDuration, setAudioDuration] = useState(0);

  // Effect for handling audio playback events
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateProgress = () => {
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
        setAudioProgress((audio.currentTime / audio.duration) * 100);
        setAudioDuration(audio.duration);
      } else {
        // Reset progress if duration is not valid (e.g., src removed)
        setAudioProgress(0);
        // We don't reset audioDuration here to 0 immediately to avoid flicker
        // if it's just a temporary state. It gets updated on loadedmetadata.
      }
    };
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => {
      setIsPlaying(false);
      setAudioProgress(0);
      if(audio) audio.currentTime = 0;
    };
    const handleLoadedMetadata = () => {
      if (audio.duration && !isNaN(audio.duration) && isFinite(audio.duration)) {
        setAudioDuration(audio.duration);
      } else {
        setAudioDuration(0); // If metadata loads with invalid duration
      }
    };
    const handleError = () => {
        // This handles errors like "no supported sources" directly from the audio element
        if (audio.error) {
            toast({
                title: "Audio Playback Error",
                description: audio.error.message || "The audio could not be played.",
                variant: "destructive",
            });
        }
        setIsPlaying(false);
        setAudioProgress(0);
        // songDataUri is not cleared here, as the source might still be technically set
        // but unplayable. Clearing it would remove the audio element.
    };


    audio.addEventListener("timeupdate", updateProgress);
    audio.addEventListener("play", handlePlay);
    audio.addEventListener("pause", handlePause);
    audio.addEventListener("ended", handleEnded);
    audio.addEventListener("loadedmetadata", handleLoadedMetadata);
    audio.addEventListener("error", handleError);


    return () => {
      audio.removeEventListener("timeupdate", updateProgress);
      audio.removeEventListener("play", handlePlay);
      audio.removeEventListener("pause", handlePause);
      audio.removeEventListener("ended", handleEnded);
      audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
      audio.removeEventListener("error", handleError);
    };
  }, [songDataUri, toast]); // Added toast to dependencies as it's used in handleError


  const handleSongUpload = async (file: File | null, dataUri: string | null) => {
    // Clear previous audio element state if it exists
    if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = ""; // Important to clear src before changing state that re-keys
        audioRef.current.load();  // Reset the media element
    }

    if (!file || !dataUri) {
      setSongFile(null);
      setSongDataUri(null);
      setSongFileName(null);
      setBeatData(null);
      setIsAnalyzing(false);
      setIsPlaying(false);
      setAudioProgress(0);
      setAudioDuration(0);
      // Toast for this case is handled by MediaPanel
      return;
    }

    setSongFile(file);
    setSongDataUri(dataUri); // This will trigger re-render of <audio> with new key & src
    setSongFileName(file.name);
    setBeatData(null);
    setIsPlaying(false);
    setAudioProgress(0);
    setAudioDuration(0); // Reset duration, will be updated by new audio element


    setIsAnalyzing(true);
    try {
      const input: AnalyzeBeatInput = { audioDataUri: dataUri };
      const result: AnalyzeBeatOutput = await analyzeBeat(input);
      setBeatData({ beats: result.beats, tempo: result.tempo });
      toast({ title: "Song Analyzed", description: `Detected ${result.beats.length} beats. Tempo: ${result.tempo.toFixed(0)} BPM.` });
    } catch (error) {
      console.error("Error analyzing beat:", error);
      toast({ title: "Analysis Error", description: "Could not analyze the song.", variant: "destructive" });
      // If analysis fails, keep the song loaded for playback attempt
      setBeatData(null);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleVisualMediaUpload = (file: File, type: "image" | "video") => {
    setVisualMediaFile(file);
    setVisualMediaType(type);
    setVisualMediaFileName(file.name);

    if (visualMediaSrc && visualMediaSrc.startsWith('blob:')) {
      URL.revokeObjectURL(visualMediaSrc);
    }
    setVisualMediaSrc(URL.createObjectURL(file));
  };

  const handleSelectPreset = (presetId: string) => {
    if (presetId.startsWith('pulse-')) {
      const intensity = presetId.split('-')[1] as 'light' | 'medium' | 'heavy';
      setPulseIntensity(intensity);
      setSelectedPreset('pulse'); // Keep the base pulse preset selected
    } else if (presetId.startsWith('glitch-')) {
      const intensity = presetId.split('-')[1] as 'light' | 'medium' | 'heavy';
      setGlitchIntensity(intensity);
      setSelectedPreset('glitch'); // Keep the base glitch preset selected
    } else {
      setSelectedPreset(presetId);
    }
    toast({ title: "Preset Selected", description: `"${presetId}" preset activated.` });
  };

  const handleCanvasBackgroundColorChange = (color: CanvasBackgroundColor) => {
    setCanvasBackgroundColor(color);
    toast({ title: "Background Changed", description: `Canvas background set to ${color}.` });
  };

  const handleBackgroundImageUpload = (file: File, dataUri: string) => {
    setBackgroundImage(dataUri);
    toast({ title: "Background Image Changed", description: `Set new background image: ${file.name}` });
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        if (songDataUri && audioRef.current.src) { // Ensure src is also set on the element
          audioRef.current.play().catch(e => {
            console.error("Error playing audio:", e);
            // Error is now primarily handled by the audio element's 'error' event listener
            // but we keep a fallback toast here if that doesn't cover it.
             if (!(e instanceof DOMException && e.name === 'AbortError')) { // AbortError is common on src change
                toast({title: "Playback Error", description: `Could not play audio. ${(e as Error).message}`, variant: "destructive"});
             }
          });
        } else {
          toast({title: "No Song Loaded", description: "Please upload a song to play or the current song is invalid.", variant: "destructive"});
        }
      }
    } else {
        toast({title: "Audio Player Not Ready", description: "Please upload a song.", variant: "destructive"});
    }
  };

  const handleRestartAudio = () => {
    if (audioRef.current && songDataUri) {
      audioRef.current.currentTime = 0;
      setAudioProgress(0);
      if (isPlaying) {
        audioRef.current.play().catch(e => console.error("Error playing audio on restart:", e));
      }
    }
  };

  const formatTime = (timeInSeconds: number): string => {
    if (isNaN(timeInSeconds) || timeInSeconds === Infinity || timeInSeconds < 0) return "0:00";
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60).toString().padStart(2, '0');
    return `${minutes}:${seconds}`;
  };

  const handleSeek = (event: React.MouseEvent<HTMLDivElement>) => {
    if (audioRef.current && audioDuration > 0 && songDataUri) {
      const progressBar = event.currentTarget;
      const rect = progressBar.getBoundingClientRect();
      const clickX = event.clientX - rect.left;
      const percentage = Math.max(0, Math.min(1, clickX / rect.width));
      const newTime = percentage * audioDuration;
      audioRef.current.currentTime = newTime;
      // setAudioProgress((newTime / audioDuration) * 100); // Let timeupdate handle this for consistency
    }
  };

  const togglePanel = (panel: PanelKey) => {
    if (activePanel === panel) {
      setIsPanelOpen(!isPanelOpen);
    } else {
      setActivePanel(panel);
      setIsPanelOpen(true);
    }
  };

  const renderPanel = () => {
    switch (activePanel) {
      case "general": return <GeneralSettingsPanel
                              canvasBackgroundColor={canvasBackgroundColor}
                              onCanvasBackgroundColorChange={handleCanvasBackgroundColorChange}
                              onBackgroundImageUpload={handleBackgroundImageUpload}
                            />;
      case "media": return <MediaPanel
                            onSongUpload={handleSongUpload}
                            onVisualMediaUpload={handleVisualMediaUpload}
                            isAnalyzing={isAnalyzing}
                            songFileName={songFileName}
                            visualMediaFileName={visualMediaFileName}
                          />;
      case "presets": return <PresetsPanel selectedPreset={selectedPreset} onSelectPreset={handleSelectPreset} pulseIntensity={pulseIntensity} glitchIntensity={glitchIntensity} />;
      case "visualizer": return <VisualizerPanel />;
      case "text": return <TextPanel />;
      case "elements": return <ElementsPanel />;
      default: return <div className="p-4">Select a section</div>;
    }
  };

  const renderPanelHeader = () => {
    switch (activePanel) {
      case "general": return "Settings";
      case "media": return "Media Library";
      case "presets": return "Visual Presets";
      case "visualizer": return "Visualizer";
      case "text": return "Text Overlay";
      case "elements": return "Elements";
      default: return "";
    }
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Sidebar */}
      <div className="w-16 flex-shrink-0 border-r border-border bg-sidebar">
        <AppLogo />
        <SidebarNav
          activePanel={activePanel}
          onSelectPanel={togglePanel}
          isPanelOpen={isPanelOpen}
        />
      </div>

      {/* Main Content Area with Settings Panel */}
      <div className="flex-1 flex">
        {/* Settings Panel - Slides in/out */}
        <div
          className={cn(
            "bg-sidebar border-r border-border transform transition-all duration-300 ease-in-out flex-shrink-0",
            isPanelOpen ? "w-[400px]" : "w-0 opacity-0"
          )}
        >
          {/* Panel Header with Collapse Button */}
          <div className={cn(
            "h-14 border-b border-border flex items-center justify-between px-4",
            !isPanelOpen && "hidden"
          )}>
            <h2 className="text-lg font-semibold">{renderPanelHeader()}</h2>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsPanelOpen(false)}
              className="hover:bg-accent"
              title="Collapse panel"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
          </div>

          <ScrollArea className={cn(
            "h-[calc(100vh-3.5rem)]",
            !isPanelOpen && "hidden"
          )}>
            <div className="p-6">
              {renderPanel()}
            </div>
          </ScrollArea>
        </div>

        {/* Main Preview and Controls */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Top Bar with Export Button */}
          <div className="h-14 border-b border-border px-4 flex justify-between items-center bg-background">
            <h2 className="text-lg font-semibold">Preview</h2>
            <ExportButton
              songDataUri={songDataUri}
              visualMediaSrc={visualMediaSrc}
              visualMediaType={visualMediaType}
              selectedPreset={selectedPreset}
              pulseIntensity={pulseIntensity}
              glitchIntensity={glitchIntensity}
            />
          </div>

          {/* Preview Area */}
          <div className="flex-1 relative p-6">
            <div className="absolute inset-0 flex items-center justify-center p-6">
              <div className="relative w-full max-w-5xl mx-auto aspect-video bg-muted rounded-lg overflow-hidden">
                <PreviewPlayer
                  visualMediaSrc={visualMediaSrc}
                  visualMediaType={visualMediaType}
                  beatData={beatData}
                  audioElementRef={audioRef}
                  isPlaying={isPlaying}
                  selectedPreset={selectedPreset}
                  songDataUriForPulse={songDataUri}
                  pulseIntensity={pulseIntensity}
                  glitchIntensity={glitchIntensity}
                  togglePlayPause={togglePlayPause}
                  canvasBackgroundColor={canvasBackgroundColor}
                  backgroundImage={backgroundImage}
                />
              </div>
            </div>
          </div>

          {/* Audio Controls */}
          <div className="border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/75 p-4">
            <div className="max-w-5xl mx-auto flex items-center gap-4">
              {/* Audio player */}
              {songDataUri && (
                <audio
                  key={songDataUri}
                  ref={audioRef}
                  src={songDataUri}
                  className="hidden"
                />
              )}

              {/* Playback Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={togglePlayPause}
                  disabled={!songDataUri}
                >
                  {isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleRestartAudio}
                  disabled={!songDataUri}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>

                {/* Time display */}
                <div className="text-sm text-muted-foreground whitespace-nowrap">
                  {formatTime((audioDuration * audioProgress) / 100)} / {formatTime(audioDuration)}
                </div>
              </div>

              {/* Progress bar */}
              <div
                className="flex-1 h-2 bg-muted rounded-full cursor-pointer relative overflow-hidden"
                onClick={handleSeek}
              >
                <div
                  className="absolute h-full bg-primary transition-all"
                  style={{ width: `${audioProgress}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}





