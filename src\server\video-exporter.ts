import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir, readdir, rm, readFile } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { spawn } from 'child_process';

// In-memory storage for job status (in production, use Redis or database)
interface JobStatus {
  status: 'processing' | 'completed' | 'error';
  progress?: number;
  message?: string;
  error?: string;
  outputPath?: string;
}

export class VideoExporterServer {
  private static jobStatuses: Map<string, JobStatus> = new Map();
  private static readonly EXPORT_DIR = path.join(process.cwd(), 'exports');

  private static delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public static async saveFrames(request: NextRequest): Promise<NextResponse> {
    try {
      const formData = await request.formData();
      const sessionId = formData.get('sessionId') as string;
      const chunkIndex = parseInt(formData.get('chunkIndex') as string);
      
      if (!sessionId) {
        return NextResponse.json({ error: 'Session ID required' }, { status: 400 });
      }

      // Create session directory
      const sessionDir = path.join(VideoExporterServer.EXPORT_DIR, sessionId);
      const framesDir = path.join(sessionDir, 'frames');
      
      if (!existsSync(sessionDir)) {
        await mkdir(sessionDir, { recursive: true });
      }
      if (!existsSync(framesDir)) {
        await mkdir(framesDir, { recursive: true });
      }

      // Save frames (now handling JPEG format)
      let frameCount = 0;
      for (const [key, value] of formData.entries()) {
        if (key.startsWith('frame_') && value instanceof File) {
          const frameIndex = key.split('_')[1];
          const globalFrameIndex = chunkIndex * 10 + parseInt(frameIndex); // Assuming CHUNK_SIZE = 10
          const framePath = path.join(framesDir, `frame_${globalFrameIndex.toString().padStart(6, '0')}.jpg`);

          const bytes = await value.arrayBuffer();
          await writeFile(framePath, Buffer.from(bytes));
          frameCount++;
        }
      }

      return NextResponse.json({ 
        success: true, 
        message: `Saved ${frameCount} frames for chunk ${chunkIndex}` 
      });

    } catch (error) {
      console.error('Error saving frames:', error);
      return NextResponse.json(
        { error: 'Failed to save frames' }, 
        { status: 500 }
      );
    }
  }

  public static async startVideoGeneration(request: NextRequest): Promise<NextResponse> {
    try {
      const formData = await request.formData();
      const sessionId = formData.get('sessionId') as string;
      const audioFile = formData.get('audio') as File;
      const fps = parseInt(formData.get('fps') as string);
      const width = parseInt(formData.get('width') as string);
      const height = parseInt(formData.get('height') as string);

      if (!sessionId || !audioFile) {
        return NextResponse.json({ error: 'Missing required data' }, { status: 400 });
      }

      const sessionDir = path.join(VideoExporterServer.EXPORT_DIR, sessionId);
      const framesDir = path.join(sessionDir, 'frames');
      const audioPath = path.join(sessionDir, 'audio.mp3');
      const outputPath = path.join(sessionDir, 'output.mp4');

      // Save audio file
      const audioBytes = await audioFile.arrayBuffer();
      await writeFile(audioPath, Buffer.from(audioBytes));

      // Initialize job status
      VideoExporterServer.jobStatuses.set(sessionId, {
        status: 'processing',
        progress: 0,
        message: 'Starting video generation...'
      });

      // Start FFmpeg process asynchronously
      VideoExporterServer._processVideoAsync(sessionId, framesDir, audioPath, outputPath, fps, width, height);

      return NextResponse.json({ 
        success: true, 
        message: 'Video generation started',
        sessionId 
      });

    } catch (error) {
      console.error('Error starting video generation:', error);
      return NextResponse.json(
        { error: 'Failed to start video generation' }, 
        { status: 500 }
      );
    }
  }

  private static async _processVideoAsync(
    sessionId: string,
    framesDir: string,
    audioPath: string,
    outputPath: string,
    fps: number,
    width: number,
    height: number
  ) {
    try {
      // Check if frames directory exists and has frames
      if (!existsSync(framesDir)) {
        throw new Error('Frames directory not found');
      }

      const frameFiles = await readdir(framesDir);
      const jpegFrames = frameFiles.filter(f => f.endsWith('.jpg')).sort();

      if (jpegFrames.length === 0) {
        throw new Error('No frames found');
      }

      VideoExporterServer.jobStatuses.set(sessionId, {
        status: 'processing',
        progress: 0.1,
        message: `Found ${jpegFrames.length} frames, starting optimized FFmpeg...`
      });

      // Optimized FFmpeg command for better performance and quality
      const ffmpegArgs = [
        '-y', // Overwrite output file
        '-framerate', fps.toString(),
        '-i', path.join(framesDir, 'frame_%06d.jpg'), // Input JPEG frames pattern
        '-i', audioPath, // Input audio
        '-c:v', 'libx264', // Video codec
        '-c:a', 'aac', // Audio codec
        '-preset', 'ultrafast', // Much faster encoding preset
        '-crf', '18', // Better quality (lower CRF for better quality/speed balance)
        '-threads', '0', // Use all available CPU cores
        '-sws_flags', 'lanczos', // Better scaling algorithm for color preservation
        '-pix_fmt', 'yuv444p', // Better color space for quality preservation
        '-movflags', '+faststart', // Optimize for web streaming
        '-shortest', // Stop encoding when shortest input ends
        '-vf', `scale=${width}:${height}:flags=lanczos`, // Ensure output dimensions with better scaling
        outputPath
      ];

      const ffmpeg = spawn('ffmpeg', ffmpegArgs, {
        stdio: ['ignore', 'pipe', 'pipe'] // stdin, stdout, stderr
      });
      
      ffmpeg.stdout.on('data', (data) => {
        // Consume stdout to prevent blocking, log if necessary
        // console.log(`FFMPEG_STDOUT: ${data.toString()}`);
      });

      let stderr = '';
      
      ffmpeg.stderr.on('data', (data) => {
        stderr += data.toString();
        
        // Parse FFmpeg progress from stderr
        const progressMatch = stderr.match(/time=(\d{2}):(\d{2}):(\d{2}\.\d{2})/);
        if (progressMatch) {
          const hours = parseInt(progressMatch[1]);
          const minutes = parseInt(progressMatch[2]);
          const seconds = parseFloat(progressMatch[3]);
          const currentTime = hours * 3600 + minutes * 60 + seconds;
          
          // Estimate total duration (rough estimate)
          const estimatedDuration = jpegFrames.length / fps;
          const progress = Math.min(0.9, Math.max(0.1, currentTime / estimatedDuration));
          
          VideoExporterServer.jobStatuses.set(sessionId, {
            status: 'processing',
            progress,
            message: `Encoding video... ${Math.round(progress * 100)}%`
          });
        }
      });

      ffmpeg.on('close', (code) => {
        if (code === 0) {
          VideoExporterServer.jobStatuses.set(sessionId, {
            status: 'completed',
            progress: 1.0,
            message: 'Video generation completed!',
            outputPath
          });
        } else {
          console.error('FFmpeg stderr:', stderr);
          VideoExporterServer.jobStatuses.set(sessionId, {
            status: 'error',
            error: `FFmpeg exited with code ${code}. Error: ${stderr.slice(-500)}` // Last 500 chars
          });
        }
      });

      ffmpeg.on('error', (error) => {
        console.error('FFmpeg error:', error);
        VideoExporterServer.jobStatuses.set(sessionId, {
          status: 'error',
          error: `FFmpeg process error: ${error.message}`
        });
      });

    } catch (error) {
      console.error('Error in _processVideoAsync:', error);
      VideoExporterServer.jobStatuses.set(sessionId, {
        status: 'error',
        error: `Processing error: ${error instanceof Error ? error.message : String(error)}`
      });
    }
  }

  public static async getJobStatus(
    request: NextRequest,
    { params }: { params: { sessionId: string } }
  ): Promise<NextResponse> {
    try {
      const sessionId = params.sessionId;
      const status = VideoExporterServer.jobStatuses.get(sessionId);

      if (!status) {
        return NextResponse.json({ error: 'Session not found' }, { status: 404 });
      }

      return NextResponse.json(status);

    } catch (error) {
      console.error('Error getting job status:', error);
      return NextResponse.json(
        { error: 'Failed to get status' }, 
        { status: 500 }
      );
    }
  }

  public static async downloadVideo(
    request: NextRequest,
    { params }: { params: { sessionId: string } }
  ): Promise<NextResponse> {
    try {
      const sessionId = params.sessionId;
      const outputPath = path.join(VideoExporterServer.EXPORT_DIR, sessionId, 'output.mp4');

      if (!existsSync(outputPath)) {
        return NextResponse.json({ error: 'Video file not found' }, { status: 404 });
      }

      const videoBuffer = await readFile(outputPath);
      
      return new NextResponse(videoBuffer, {
        status: 200,
        headers: {
          'Content-Type': 'video/mp4',
          'Content-Length': videoBuffer.length.toString(),
          'Content-Disposition': `attachment; filename="rhythmic-canvas-${sessionId}.mp4"`
        },
      });

    } catch (error) {
      console.error('Error downloading video:', error);
      return NextResponse.json(
        { error: 'Failed to download video' }, 
        { status: 500 }
      );
    }
  }

  public static async cleanupSession(
    request: NextRequest,
    { params }: { params: { sessionId: string } }
  ): Promise<NextResponse> {
    try {
      const sessionId = params.sessionId;
      const sessionDir = path.join(VideoExporterServer.EXPORT_DIR, sessionId);

      if (!existsSync(sessionDir)) {
        return NextResponse.json({ error: 'Session directory not found' }, { status: 404 });
      }

      // Remove the entire session directory with retry for EBUSY
      const maxRetries = 5;
      for (let i = 0; i < maxRetries; i++) {
        try {
          await rm(sessionDir, { recursive: true, force: true });
          break; // Success, exit loop
        } catch (error: any) {
          if (error.code === 'EBUSY' && i < maxRetries - 1) {
            console.warn(`EBUSY error during cleanup, retrying (${i + 1}/${maxRetries})...`);
            await VideoExporterServer.delay(500 * (i + 1)); // Exponential backoff
          } else {
            throw error; // Re-throw if not EBUSY or max retries reached
          }
        }
      }

      // Clean up in-memory job status if it exists
      if (VideoExporterServer.jobStatuses.has(sessionId)) {
        VideoExporterServer.jobStatuses.delete(sessionId);
      }

      return NextResponse.json({ 
        success: true,
        message: `Cleaned up session ${sessionId}`
      });

    } catch (error) {
      console.error('Error cleaning up session:', error);
      return NextResponse.json(
        { error: 'Failed to cleanup session' }, 
        { status: 500 }
      );
    }
  }
}