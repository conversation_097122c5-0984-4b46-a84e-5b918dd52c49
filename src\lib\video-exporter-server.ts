// video-exporter-server.ts
// Hybrid client-server video export implementation for Rhythmic Canvas

interface ExportOptions {
  songDataUri: string;
  visualMediaSrc: string;
  visualMediaType: "image" | "video";
  outputWidth: number;
  outputHeight: number;
  fps: number;
  selectedPreset?: string | null;
  pulseIntensity?: 'light' | 'medium' | 'heavy';
  glitchIntensity?: 'light' | 'medium' | 'heavy';
  onProgress?: (progress: number, info?: string) => void;
}

interface ExportResult {
  blob: Blob;
  duration: number;
}

// Frame rendering constants (matching preview-player.tsx)
const ANALYSIS_WINDOW_DURATION_SEC = 0.05;

const PULSE_LEVELS = {
  light: { maxScale: 0.1, rmsBoost: 2.0 },
  medium: { maxScale: 0.2, rmsBoost: 3.0 },
  heavy: { maxScale: 0.3, rmsBoost: 4.0 }
} as const;

const GLITCH_LEVELS = {
  light: {
    maxDisplacement: 5, noiseIntensity: 0.02, channelSeparation: 2,
    compressionBlocks: 3, scanLineIntensity: 0.1, rmsBoost: 2.0
  },
  medium: {
    maxDisplacement: 10, noiseIntensity: 0.05, channelSeparation: 4,
    compressionBlocks: 6, scanLineIntensity: 0.2, rmsBoost: 3.0
  },
  heavy: {
    maxDisplacement: 20, noiseIntensity: 0.1, channelSeparation: 8,
    compressionBlocks: 10, scanLineIntensity: 0.3, rmsBoost: 4.0
  }
} as const;

/**
 * Client-side frame renderer that matches the preview exactly
 */
class FrameRenderer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private audioContext: AudioContext;
  private audioBuffer: AudioBuffer | null = null;
  private visualElement: HTMLImageElement | HTMLVideoElement | null = null;
  private options: ExportOptions;
  private worker: Worker | null = null;
  private workerPromises: Map<string, { resolve: (imageData: ImageData) => void; reject: (error: Error) => void }> = new Map();
  private useWorker: boolean = false;

  constructor(options: ExportOptions) {
    this.options = options;
    this.canvas = document.createElement('canvas');
    this.canvas.width = options.outputWidth;
    this.canvas.height = options.outputHeight;
    this.ctx = this.canvas.getContext('2d')!;
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

    // Initialize Web Worker for parallel processing (only for images, not videos)
    if (options.visualMediaType === 'image') {
      try {
        this.worker = new Worker(new URL('../workers/frame-renderer.worker.ts', import.meta.url));
        this.worker.onmessage = this.handleWorkerMessage.bind(this);
        this.worker.onerror = (error) => {
          console.error('Worker error:', error);
          this.worker = null;
          this.useWorker = false;
        };
        this.useWorker = true;
      } catch (error) {
        console.warn('Failed to initialize Web Worker, falling back to main thread:', error);
        this.worker = null;
        this.useWorker = false;
      }
    }
  }

  private handleWorkerMessage(event: MessageEvent): void {
    const { id, imageData, error } = event.data;
    const promise = this.workerPromises.get(id);

    if (promise) {
      this.workerPromises.delete(id);
      if (error) {
        promise.reject(new Error(error));
      } else {
        promise.resolve(imageData);
      }
    }
  }

  async initialize(): Promise<void> {
    // Load audio buffer for analysis
    const audioResponse = await fetch(this.options.songDataUri);
    const audioArrayBuffer = await audioResponse.arrayBuffer();
    this.audioBuffer = await this.audioContext.decodeAudioData(audioArrayBuffer);

    // Load visual media
    if (this.options.visualMediaType === 'image') {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      await new Promise((resolve, reject) => {
        img.onload = resolve;
        img.onerror = reject;
        img.src = this.options.visualMediaSrc;
      });
      this.visualElement = img;
    } else if (this.options.visualMediaType === 'video') {
      const video = document.createElement('video');
      video.crossOrigin = 'anonymous';
      video.muted = true;
      await new Promise((resolve, reject) => {
        video.onloadeddata = resolve;
        video.onerror = reject;
        video.src = this.options.visualMediaSrc;
      });
      this.visualElement = video;
    }
  }

  private calculateAudioEnergyAtTime(currentTime: number, intensityType: 'pulse' | 'glitch'): number {
    if (!this.audioBuffer) return 0;

    const sampleRate = this.audioBuffer.sampleRate;
    const windowSamples = Math.floor(ANALYSIS_WINDOW_DURATION_SEC * sampleRate);
    
    let startSample = Math.max(0, Math.floor(currentTime * sampleRate));
    let endSample = Math.min(startSample + windowSamples, this.audioBuffer.length);
    
    if (startSample >= endSample) return 0;

    const channelData = this.audioBuffer.getChannelData(0);
    const segment = channelData.slice(startSample, endSample);
    
    if (segment.length === 0) return 0;

    let sumOfSquares = 0;
    for (let i = 0; i < segment.length; i++) {
      sumOfSquares += segment[i] * segment[i];
    }
    
    const rms = Math.sqrt(sumOfSquares / segment.length);
    const boostLevel = intensityType === 'pulse' 
      ? PULSE_LEVELS[this.options.pulseIntensity || 'heavy'].rmsBoost
      : GLITCH_LEVELS[this.options.glitchIntensity || 'heavy'].rmsBoost;
    
    return Math.min(rms * boostLevel, 1.0);
  }

  async renderFrame(timeSeconds: number): Promise<ImageData> {
    // Use Web Worker for image rendering if available
    if (this.useWorker && this.worker && this.options.visualMediaType === 'image') {
      return this.renderFrameWithWorker(timeSeconds);
    }

    // Fallback to main thread rendering
    return this.renderFrameMainThread(timeSeconds);
  }

  private async renderFrameWithWorker(timeSeconds: number): Promise<ImageData> {
    const requestId = Math.random().toString(36).substr(2, 9);

    const request = {
      id: requestId,
      timeSeconds,
      width: this.canvas.width,
      height: this.canvas.height,
      visualMediaSrc: this.options.visualMediaSrc,
      visualMediaType: this.options.visualMediaType,
      selectedPreset: this.options.selectedPreset || 'pulse',
      pulseIntensity: this.options.pulseIntensity || 'heavy',
      glitchIntensity: this.options.glitchIntensity || 'heavy',
      audioBuffer: this.audioBuffer?.getChannelData(0).buffer,
      canvasBackgroundColor: '#000000' // Default background
    };

    return new Promise((resolve, reject) => {
      this.workerPromises.set(requestId, { resolve, reject });
      this.worker!.postMessage(request);

      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.workerPromises.has(requestId)) {
          this.workerPromises.delete(requestId);
          reject(new Error('Worker timeout'));
        }
      }, 30000);
    });
  }

  private async renderFrameMainThread(timeSeconds: number): Promise<ImageData> {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    if (!this.visualElement) {
      return this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    }

    // Update video time if it's a video
    if (this.visualElement instanceof HTMLVideoElement) {
      this.visualElement.currentTime = timeSeconds;
      // Wait for video to seek
      await new Promise(resolve => setTimeout(resolve, 16)); // ~1 frame at 60fps
    }

    // Calculate effects based on audio
    let scale = 1.0;
    let glitchParams = {
      displacement: 0, channelSeparation: 0, noiseLevel: 0,
      compressionLevel: 0, scanLinePosition: 0, scanLineIntensity: 0
    };

    if (this.options.selectedPreset === 'pulse') {
      const audioEnergy = this.calculateAudioEnergyAtTime(timeSeconds, 'pulse');
      const pulseLevel = PULSE_LEVELS[this.options.pulseIntensity || 'heavy'];
      scale = 1.0 + (audioEnergy * pulseLevel.maxScale);
    } else if (this.options.selectedPreset === 'glitch') {
      const audioEnergy = this.calculateAudioEnergyAtTime(timeSeconds, 'glitch');
      const glitchLevel = GLITCH_LEVELS[this.options.glitchIntensity || 'heavy'];
      
      glitchParams = {
        displacement: audioEnergy * glitchLevel.maxDisplacement * (Math.random() - 0.5) * 2,
        channelSeparation: audioEnergy * glitchLevel.channelSeparation,
        noiseLevel: audioEnergy * glitchLevel.noiseIntensity,
        compressionLevel: audioEnergy * glitchLevel.compressionBlocks,
        scanLinePosition: Math.random(),
        scanLineIntensity: audioEnergy * glitchLevel.scanLineIntensity
      };
    }

    // Save context for transformations
    this.ctx.save();

    // Apply transformations
    if (this.options.selectedPreset === 'pulse') {
      // Center scaling
      this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);
      this.ctx.scale(scale, scale);
      this.ctx.translate(-this.canvas.width / 2, -this.canvas.height / 2);
    } else if (this.options.selectedPreset === 'glitch') {
      // Apply displacement
      this.ctx.translate(glitchParams.displacement, 0);
    }

    // Draw the visual media
    this.ctx.drawImage(this.visualElement, 0, 0, this.canvas.width, this.canvas.height);

    // Restore context
    this.ctx.restore();

    // Apply glitch overlays if needed
    if (this.options.selectedPreset === 'glitch') {
      this.applyGlitchEffects(glitchParams);
    }

    return this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
  }

  private applyGlitchEffects(params: any): void {
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;

    // Apply channel separation and noise
    for (let i = 0; i < data.length; i += 4) {
      const intensity = params.noiseLevel;
      
      // Channel separation effect
      const offset = Math.floor(params.channelSeparation);
      if (i + offset * 4 < data.length) {
        data[i] = Math.min(255, data[i] + data[i + offset * 4] * intensity * 255); // Red channel
      }
      
      // Add noise
      const noise = (Math.random() - 0.5) * intensity * 255;
      data[i] = Math.max(0, Math.min(255, data[i] + noise));
      data[i + 1] = Math.max(0, Math.min(255, data[i + 1] + noise));
      data[i + 2] = Math.max(0, Math.min(255, data[i + 2] + noise));
    }

    // Apply scan lines
    if (params.scanLineIntensity > 0) {
      const scanY = Math.floor(params.scanLinePosition * this.canvas.height);
      const lineWidth = 2;
      
      for (let y = Math.max(0, scanY - lineWidth); y < Math.min(this.canvas.height, scanY + lineWidth); y++) {
        for (let x = 0; x < this.canvas.width; x++) {
          const idx = (y * this.canvas.width + x) * 4;
          const intensity = params.scanLineIntensity * 255;
          data[idx] = Math.min(255, data[idx] + intensity);
          data[idx + 1] = Math.min(255, data[idx + 1] + intensity);
          data[idx + 2] = Math.min(255, data[idx + 2] + intensity);
        }
      }
    }

    this.ctx.putImageData(imageData, 0, 0);
  }

  dispose(): void {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
  }
}

/**
 * Chunk-based frame sender to prevent memory issues
 */
class ChunkedFrameSender {
  private readonly CHUNK_SIZE = 10; // frames per chunk
  private readonly MAX_RETRIES = 3;

  async sendFramesInChunks(
    frames: Blob[],
    sessionId: string,
    onProgress?: (progress: number, info?: string) => void
  ): Promise<void> {
    const totalChunks = Math.ceil(frames.length / this.CHUNK_SIZE);
    
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const startIdx = chunkIndex * this.CHUNK_SIZE;
      const endIdx = Math.min(startIdx + this.CHUNK_SIZE, frames.length);
      const chunkFrames = frames.slice(startIdx, endIdx);
      
      await this.sendChunkWithRetry(chunkFrames, sessionId, chunkIndex, totalChunks);
      
      const progress = (chunkIndex + 1) / totalChunks * 0.8; // 80% for sending frames
      onProgress?.(progress, `Sent chunk ${chunkIndex + 1}/${totalChunks}`);
    }
  }

  private async sendChunkWithRetry(
    frames: Blob[],
    sessionId: string,
    chunkIndex: number,
    totalChunks: number,
    retryCount = 0
  ): Promise<void> {
    try {
      const formData = new FormData();
      formData.append('sessionId', sessionId);
      formData.append('chunkIndex', chunkIndex.toString());
      formData.append('totalChunks', totalChunks.toString());
      
      frames.forEach((frame, idx) => {
        formData.append(`frame_${idx}`, frame, `frame_${chunkIndex * this.CHUNK_SIZE + idx}.jpg`);
      });

      const response = await fetch('/api/export/frames', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Server responded with ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      if (retryCount < this.MAX_RETRIES) {
        console.warn(`Chunk ${chunkIndex} failed, retrying (${retryCount + 1}/${this.MAX_RETRIES}):`, error);
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000)); // Exponential backoff
        return this.sendChunkWithRetry(frames, sessionId, chunkIndex, totalChunks, retryCount + 1);
      }
      throw new Error(`Failed to send chunk ${chunkIndex} after ${this.MAX_RETRIES} retries: ${error}`);
    }
  }
}

/**
 * Main export function using hybrid client-server approach
 */
export async function exportWithImprovedServerFFmpeg(options: ExportOptions): Promise<ExportResult> {
  const {
    songDataUri,
    outputWidth,
    outputHeight,
    fps,
    onProgress
  } = options;

  onProgress?.(0, 'Initializing export...');

  // Create session ID for this export
  const sessionId = `export_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  let frameRenderer: FrameRenderer | null = null;
  
  try {
    // Initialize frame renderer
    frameRenderer = new FrameRenderer(options);
    await frameRenderer.initialize();
    
    onProgress?.(0.05, 'Analyzing audio duration...');

    // Get audio duration
    const audio = new Audio();
    const audioDuration = await new Promise<number>((resolve, reject) => {
      audio.onloadedmetadata = () => resolve(audio.duration);
      audio.onerror = reject;
      audio.src = songDataUri;
    });

    const totalFrames = Math.ceil(audioDuration * fps);
    onProgress?.(0.1, `Rendering ${totalFrames} frames...`);

    // Generate frames with enhanced progress tracking
    const frames: Blob[] = [];
    const frameInterval = 1 / fps;
    const startTime = Date.now();

    for (let frameIndex = 0; frameIndex < totalFrames; frameIndex++) {
      const timeSeconds = frameIndex * frameInterval;

      // Render frame
      const frameStartTime = Date.now();
      const imageData = await frameRenderer.renderFrame(timeSeconds);
      const frameRenderTime = Date.now() - frameStartTime;

      // Convert to blob using JPEG for better compression and faster transfer
      const canvas = document.createElement('canvas');
      canvas.width = outputWidth;
      canvas.height = outputHeight;
      const ctx = canvas.getContext('2d')!;
      ctx.putImageData(imageData, 0, 0);

      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/jpeg', 0.85); // JPEG with 85% quality for better compression
      });

      frames.push(blob);

      // Enhanced progress tracking with time estimates
      const frameProgress = 0.1 + (frameIndex / totalFrames) * 0.3;
      const elapsedTime = Date.now() - startTime;
      const avgTimePerFrame = elapsedTime / (frameIndex + 1);
      const remainingFrames = totalFrames - frameIndex - 1;
      const estimatedTimeRemaining = (avgTimePerFrame * remainingFrames) / 1000; // in seconds

      const progressMessage = `Rendered frame ${frameIndex + 1}/${totalFrames} (${frameRenderTime}ms/frame, ~${Math.round(estimatedTimeRemaining)}s remaining)`;
      onProgress?.(frameProgress, progressMessage);
    }

    onProgress?.(0.4, 'Sending frames to server...');

    // Send frames in chunks
    const frameSender = new ChunkedFrameSender();
    await frameSender.sendFramesInChunks(frames, sessionId, (chunkProgress, info) => {
      const totalProgress = 0.4 + chunkProgress * 0.4; // 0.4 to 0.8
      onProgress?.(totalProgress, info);
    });

    onProgress?.(0.8, 'Server processing video with optimized settings...');

    // Send audio and trigger video generation
    const audioBlob = await fetch(songDataUri).then(r => r.blob());
    const formData = new FormData();
    formData.append('sessionId', sessionId);
    formData.append('audio', audioBlob, 'audio.mp3');
    formData.append('fps', fps.toString());
    formData.append('width', outputWidth.toString());
    formData.append('height', outputHeight.toString());
    formData.append('totalFrames', totalFrames.toString()); // Add frame count for validation
    formData.append('preset', options.selectedPreset || 'pulse'); // Add preset info

    const generateResponse = await fetch('/api/export/generate', {
      method: 'POST',
      body: formData,
    });

    if (!generateResponse.ok) {
      const errorText = await generateResponse.text();
      throw new Error(`Video generation failed: ${errorText}`);
    }

    // Poll for completion
    let videoBlob: Blob | null = null;
    const pollInterval = 2000; // 2 seconds
    const maxPollTime = 300000; // 5 minutes
    let pollStartTime = Date.now();

    while (!videoBlob && (Date.now() - pollStartTime) < maxPollTime) {
      await new Promise(resolve => setTimeout(resolve, pollInterval));
      
      const statusResponse = await fetch(`/api/export/status/${sessionId}`);
      
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        
        if (statusData.status === 'completed') {
          // Download the video
          const downloadResponse = await fetch(`/api/export/download/${sessionId}`);
          if (downloadResponse.ok) {
            videoBlob = await downloadResponse.blob();
          }
        } else if (statusData.status === 'error') {
          throw new Error(`Server processing failed: ${statusData.error}`);
        } else if (statusData.progress) {
          const serverProgress = 0.8 + (statusData.progress * 0.2); // 0.8 to 1.0
          onProgress?.(serverProgress, statusData.message || 'Processing...');
        }
      }
    }

    if (!videoBlob) {
      throw new Error('Video generation timed out');
    }

    onProgress?.(1.0, 'Export completed!');

    return {
      blob: videoBlob,
      duration: audioDuration
    };

  } finally {
    // Cleanup
    frameRenderer?.dispose();
    
    // Clean up server resources
    try {
      await fetch(`/api/export/cleanup/${sessionId}`, { method: 'DELETE' });
    } catch (error) {
      console.warn('Failed to cleanup server resources:', error);
    }
  }
}