import { NextRequest } from 'next/server';
import { writeFile } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import os from 'os';

export async function POST(request: NextRequest) {
  try {
    // Get the form data
    const formData = await request.formData();
    const jobId = formData.get('jobId') as string;
    const audioFile = formData.get('audio') as File;
    
    // Validate required parameters
    if (!jobId || !audioFile) {
      return new Response(
        JSON.stringify({ error: 'Missing jobId or audio file' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Check if job directory exists
    const jobDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports', jobId);
    if (!existsSync(jobDir)) {
      return new Response(
        JSON.stringify({ error: 'Job not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Log audio file details for debugging
    console.log(`Audio file received: ${audioFile.name}, type: ${audioFile.type}, size: ${audioFile.size} bytes`);
    
    // Save audio file
    const audioBuffer = Buffer.from(await audioFile.arrayBuffer());
    const audioPath = path.join(jobDir, 'audio.mp3');
    await writeFile(audioPath, audioBuffer);
    
    console.log(`Audio file saved for job ${jobId}: ${audioPath}, size: ${audioBuffer.length} bytes`);
    
    // Return success
    return new Response(
      JSON.stringify({ success: true }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error uploading audio:', error);
    return new Response(
      JSON.stringify({ error: `Failed to upload audio: ${error}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}
