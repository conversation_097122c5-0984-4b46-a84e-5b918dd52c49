import { NextRequest } from 'next/server';
import { existsSync, createReadStream, statSync } from 'fs';
import path from 'path';
import os from 'os';
import { unlink } from 'fs/promises';

export async function GET(request: NextRequest) {
  try {
    // Get jobId from query parameters
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');

    // Validate required parameters
    if (!jobId) {
      return new Response(
        JSON.stringify({ error: 'Missing jobId' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Check if output file exists
    const jobDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports', jobId);
    const outputPath = path.join(jobDir, 'output.mp4');

    if (!existsSync(outputPath)) {
      return new Response(
        JSON.stringify({ error: 'Video not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }

    // Get file stats for content length
    const stats = statSync(outputPath);
    const fileSize = stats.size;

    console.log(`Downloading video for job ${jobId}, file size: ${fileSize} bytes`);

    // Create a readable stream for the file
    const stream = createReadStream(outputPath);

    // Schedule cleanup after a delay (give time for download to complete)
    setTimeout(async () => {
      try {
        await unlink(outputPath);
        console.log(`Deleted output file for job ${jobId}`);
      } catch (error) {
        console.error(`Error deleting output file for job ${jobId}:`, error);
      }
    }, 30000); // Increased timeout to 30 seconds

    // Convert Node.js stream to Web Stream
    const webStream = new ReadableStream({
      start(controller) {
        stream.on('data', (chunk) => {
          controller.enqueue(new Uint8Array(chunk));
        });

        stream.on('end', () => {
          controller.close();
        });

        stream.on('error', (error) => {
          console.error('Stream error:', error);
          controller.error(error);
        });
      }
    });

    // Return the video file with proper headers for streaming
    return new Response(webStream, {
      headers: {
        'Content-Type': 'video/mp4',
        'Content-Length': fileSize.toString(),
        'Content-Disposition': 'attachment; filename="rhythmic-canvas-video.mp4"',
        'Cache-Control': 'no-cache',
        'Accept-Ranges': 'bytes'
      }
    });
  } catch (error) {
    console.error('Error downloading video:', error);
    return new Response(
      JSON.stringify({ error: `Failed to download video: ${error}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}