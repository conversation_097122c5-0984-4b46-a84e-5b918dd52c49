// debug-export.js
// Simple debugging script to test export functionality

console.log('🔍 Starting Export Debug Session...');

// Check if we're in a browser environment
if (typeof window === 'undefined') {
  console.log('❌ This script needs to run in a browser environment');
  process.exit(1);
}

// Check for required APIs
const checkAPIs = () => {
  const checks = [
    { name: 'AudioContext', available: !!(window.AudioContext || window.webkitAudioContext) },
    { name: 'Canvas', available: !!document.createElement('canvas').getContext },
    { name: 'Web Workers', available: !!window.Worker },
    { name: 'Fetch API', available: !!window.fetch },
    { name: 'Blob', available: !!window.Blob },
    { name: 'URL.createObjectURL', available: !!window.URL?.createObjectURL }
  ];

  console.log('🔧 API Availability Check:');
  checks.forEach(check => {
    console.log(`  ${check.available ? '✅' : '❌'} ${check.name}: ${check.available ? 'Available' : 'Not Available'}`);
  });

  return checks.every(check => check.available);
};

// Test AudioContext creation
const testAudioContext = () => {
  console.log('🎵 Testing AudioContext...');
  try {
    const audioContext = new (window.AudioContext || window.webkitAudioContext)();
    console.log('✅ AudioContext created successfully');
    console.log(`  Sample Rate: ${audioContext.sampleRate}Hz`);
    console.log(`  State: ${audioContext.state}`);
    audioContext.close();
    return true;
  } catch (error) {
    console.error('❌ AudioContext creation failed:', error);
    return false;
  }
};

// Test Canvas creation
const testCanvas = () => {
  console.log('🎨 Testing Canvas...');
  try {
    const canvas = document.createElement('canvas');
    canvas.width = 640;
    canvas.height = 480;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      throw new Error('Failed to get 2D context');
    }
    
    // Test basic drawing
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 100, 100);
    
    // Test image data
    const imageData = ctx.getImageData(0, 0, 100, 100);
    console.log('✅ Canvas operations successful');
    console.log(`  Canvas size: ${canvas.width}x${canvas.height}`);
    console.log(`  ImageData size: ${imageData.width}x${imageData.height}`);
    
    return true;
  } catch (error) {
    console.error('❌ Canvas test failed:', error);
    return false;
  }
};

// Test Web Worker creation
const testWebWorker = () => {
  console.log('⚡ Testing Web Worker...');
  try {
    // Create a simple inline worker for testing
    const workerCode = `
      self.onmessage = function(e) {
        self.postMessage({ result: 'Worker is working!', data: e.data });
      };
    `;
    
    const blob = new Blob([workerCode], { type: 'application/javascript' });
    const workerUrl = URL.createObjectURL(blob);
    const worker = new Worker(workerUrl);
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        console.log('⚠️ Web Worker test timed out');
        worker.terminate();
        URL.revokeObjectURL(workerUrl);
        resolve(false);
      }, 2000);
      
      worker.onmessage = (e) => {
        console.log('✅ Web Worker test successful');
        console.log(`  Worker response: ${e.data.result}`);
        clearTimeout(timeout);
        worker.terminate();
        URL.revokeObjectURL(workerUrl);
        resolve(true);
      };
      
      worker.onerror = (error) => {
        console.error('❌ Web Worker test failed:', error);
        clearTimeout(timeout);
        worker.terminate();
        URL.revokeObjectURL(workerUrl);
        resolve(false);
      };
      
      worker.postMessage({ test: 'Hello Worker!' });
    });
  } catch (error) {
    console.error('❌ Web Worker creation failed:', error);
    return Promise.resolve(false);
  }
};

// Test data URI creation
const testDataURI = () => {
  console.log('📄 Testing Data URI creation...');
  try {
    // Test image data URI
    const canvas = document.createElement('canvas');
    canvas.width = 10;
    canvas.height = 10;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = '#00ff00';
    ctx.fillRect(0, 0, 10, 10);
    
    const imageDataUri = canvas.toDataURL('image/jpeg', 0.8);
    console.log('✅ Image Data URI created');
    console.log(`  Length: ${imageDataUri.length} characters`);
    
    // Test blob URL
    const blob = new Blob(['test data'], { type: 'text/plain' });
    const blobUrl = URL.createObjectURL(blob);
    console.log('✅ Blob URL created');
    console.log(`  URL: ${blobUrl}`);
    URL.revokeObjectURL(blobUrl);
    
    return true;
  } catch (error) {
    console.error('❌ Data URI test failed:', error);
    return false;
  }
};

// Main debug function
const runDebugTests = async () => {
  console.log('🚀 Running comprehensive debug tests...\n');
  
  const results = {
    apiCheck: checkAPIs(),
    audioContext: testAudioContext(),
    canvas: testCanvas(),
    webWorker: await testWebWorker(),
    dataUri: testDataURI()
  };
  
  console.log('\n📊 DEBUG RESULTS SUMMARY:');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`  ${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASS' : 'FAIL'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  
  if (allPassed) {
    console.log('\n🎉 All debug tests passed! The environment should support video export.');
    console.log('\n💡 If you\'re still seeing errors, they might be related to:');
    console.log('  • Network issues (CORS, fetch failures)');
    console.log('  • Server-side processing (FFmpeg, file handling)');
    console.log('  • Specific media file formats or sizes');
    console.log('  • Browser security restrictions');
  } else {
    console.log('\n⚠️ Some debug tests failed. This may cause export issues.');
    console.log('\n🔧 Recommendations:');
    if (!results.apiCheck) console.log('  • Update your browser to support modern web APIs');
    if (!results.audioContext) console.log('  • Check browser audio permissions and policies');
    if (!results.canvas) console.log('  • Ensure hardware acceleration is enabled');
    if (!results.webWorker) console.log('  • Check if Web Workers are blocked by security policies');
    if (!results.dataUri) console.log('  • Verify blob and data URI support');
  }
  
  return results;
};

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runDebugTests };
}

// Auto-run if loaded directly
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDebugTests);
  } else {
    runDebugTests();
  }
}
