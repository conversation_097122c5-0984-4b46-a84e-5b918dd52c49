import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google'; // Using Inter as a fallback, <PERSON><PERSON><PERSON> is preferred
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { Geist } from 'next/font/google'; // Corrected import for Geist
import { Geist_Mono } from 'next/font/google'; // Corrected import for Geist Mono

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Rhythmic Canvas',
  description: 'Create stunning music videos with synchronized effects.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark">
      <body className={`${geistSans.variable} ${geistMono.variable} font-sans antialiased`}>
        {children}
        <Toaster />
      </body>
    </html>
  );
}
