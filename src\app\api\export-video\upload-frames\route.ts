import { NextRequest } from 'next/server';
import { writeFile } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import os from 'os';

export async function POST(request: NextRequest) {
  try {
    // Get the form data
    const formData = await request.formData();
    const jobId = formData.get('jobId') as string;
    const startFrame = parseInt(formData.get('startFrame') as string, 10);
    const frameCount = parseInt(formData.get('frameCount') as string, 10);
    
    // Validate required parameters
    if (!jobId || isNaN(startFrame) || isNaN(frameCount)) {
      return new Response(
        JSON.stringify({ error: 'Missing jobId, startFrame, or frameCount' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Check if job directory exists
    const jobDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports', jobId);
    const framesDir = path.join(jobDir, 'frames');
    if (!existsSync(jobDir) || !existsSync(framesDir)) {
      return new Response(
        JSON.stringify({ error: 'Job not found' }),
        { status: 404, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Process each frame
    const framePromises = [];
    
    for (let i = 0; i < frameCount; i++) {
      const frameIndex = startFrame + i;
      const frame = formData.get(`frame_${i}`) as string;
      
      if (!frame) {
        console.warn(`Frame ${frameIndex} not found in form data`);
        continue;
      }
      
      framePromises.push((async () => {
        const frameData = frame.replace(/^data:image\/\w+;base64,/, '');
        const frameBuffer = Buffer.from(frameData, 'base64');
        const framePath = path.join(framesDir, `frame_${frameIndex.toString().padStart(6, '0')}.jpg`);
        await writeFile(framePath, frameBuffer);
        return framePath;
      })());
    }
    
    // Wait for all frames to be written
    await Promise.all(framePromises);
    
    console.log(`Saved ${framePromises.length} frames for job ${jobId} (frames ${startFrame}-${startFrame + frameCount - 1})`);
    
    // Return success
    return new Response(
      JSON.stringify({ 
        success: true,
        framesProcessed: framePromises.length
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error uploading frames:', error);
    return new Response(
      JSON.stringify({ error: `Failed to upload frames: ${error}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}