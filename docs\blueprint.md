# **App Name**: Rhythmic Canvas

## Core Features:

- Media Upload: Upload audio, video, or images to use as input for the music video.
- Real-time Preview: Preview the music video with applied effects in real-time.
- Beat Detection: Analyze the uploaded song to identify beats and rhythm using AI as a tool for its reasoning about how best to select the best moments to pulse.
- Pulsing Effect: Apply a pulsing effect to the uploaded media, synchronizing it with the beat of the song.
- Video Export: Export the created music video to MP4 format.

## Style Guidelines:

- Sidebar for settings (General, Presets, Media, Visualizer, Text, Elements).
- Preset placeholders: Pulse, Particles, Paint, Sketch, Blur, Glitch, Static, Color Shifts, Kaleidoscope, Lighting (<PERSON>low, Neon).
- Sleek, dark, modern theme as per <PERSON>pecterr.
- Subtle transitions and animations for a smooth user experience.
- Accent: Electric blue (#7DF9FF) for interactive elements.
- Clean and minimalistic icons for sidebar options and controls.