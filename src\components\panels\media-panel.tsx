"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Film, Music, Upload, Loader2 } from "lucide-react";
import { useCallback } from "react";
import { useDropzone } from "react-dropzone";

interface MediaPanelProps {
  onSongUpload: (file: File | null, dataUri: string | null) => void;
  onVisualMediaUpload: (file: File, type: "image" | "video") => void;
  isAnalyzing: boolean;
  songFileName: string | null;
  visualMediaFileName: string | null;
}

export default function MediaPanel({
  onSongUpload,
  onVisualMediaUpload,
  isAnalyzing,
  songFileName,
  visualMediaFileName,
}: MediaPanelProps) {
  const onSongDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        onSongUpload(file, reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  }, [onSongUpload]);

  const onVisualDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      const type = file.type.startsWith('image/') ? 'image' : 'video';
      onVisualMediaUpload(file, type);
    }
  }, [onVisualMediaUpload]);

  const {
    getRootProps: getSongRootProps,
    getInputProps: getSongInputProps,
    isDragActive: isSongDragActive
  } = useDropzone({
    onDrop: onSongDrop,
    accept: {
      'audio/*': ['.mp3', '.wav', '.m4a', '.aac']
    },
    maxFiles: 1
  });

  const {
    getRootProps: getVisualRootProps,
    getInputProps: getVisualInputProps,
    isDragActive: isVisualDragActive
  } = useDropzone({
    onDrop: onVisualDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif'],
      'video/*': ['.mp4', '.webm', '.mov']
    },
    maxFiles: 1
  });

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Media Library</h2>
        <p className="text-muted-foreground">
          Upload your song, images, or video clips.
        </p>
      </div>

      <div className="space-y-4">
        {/* Audio Upload */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Music className="h-5 w-5" />
            Audio Track
          </h3>
          <Card
            {...getSongRootProps()}
            className="border-2 border-dashed hover:border-primary/50 transition-colors cursor-pointer p-6"
          >
            <input {...getSongInputProps()} />
            <div className="flex flex-col items-center justify-center text-center space-y-2">
              {isAnalyzing ? (
                <>
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                  <p className="text-muted-foreground">Analyzing audio...</p>
                </>
              ) : (
                <>
                  <Upload className="h-8 w-8 text-muted-foreground mb-2" />
                  {songFileName ? (
                    <>
                      <p className="font-medium">{songFileName}</p>
                      <p className="text-sm text-muted-foreground">
                        Click or drag to replace
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="font-medium">
                        {isSongDragActive ? "Drop the audio file here" : "Upload audio"}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Drag & drop or click to select
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Supports MP3, WAV, M4A, AAC
                      </p>
                    </>
                  )}
                </>
              )}
            </div>
          </Card>
        </div>

        {/* Visual Media Upload */}
        <div>
          <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
            <Film className="h-5 w-5" />
            Visual Media
          </h3>
          <Card
            {...getVisualRootProps()}
            className="border-2 border-dashed hover:border-primary/50 transition-colors cursor-pointer p-6"
          >
            <input {...getVisualInputProps()} />
            <div className="flex flex-col items-center justify-center text-center space-y-2">
              <Upload className="h-8 w-8 text-muted-foreground mb-2" />
              {visualMediaFileName ? (
                <>
                  <p className="font-medium">{visualMediaFileName}</p>
                  <p className="text-sm text-muted-foreground">
                    Click or drag to replace
                  </p>
                </>
              ) : (
                <>
                  <p className="font-medium">
                    {isVisualDragActive ? "Drop the file here" : "Upload image or video"}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Drag & drop or click to select
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supports PNG, JPG, GIF, MP4, WebM, MOV
                  </p>
                </>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
