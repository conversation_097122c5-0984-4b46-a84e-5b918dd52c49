import { NextRequest } from 'next/server';
import { mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import os from 'os';

// Ensure temp directory exists
async function ensureTempDir() {
  const tempDir = path.join(os.tmpdir(), 'rhythmic-canvas-exports');
  if (!existsSync(tempDir)) {
    await mkdir(tempDir, { recursive: true });
  }
  return tempDir;
}

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const { duration, fps, width, height } = await request.json();
    
    // Validate required parameters
    if (!duration || !fps || !width || !height) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        { status: 400, headers: { 'Content-Type': 'application/json' } }
      );
    }
    
    // Create a unique ID for this export job
    const jobId = uuidv4();
    const tempDir = await ensureTempDir();
    const jobDir = path.join(tempDir, jobId);
    
    // Create job directory
    await mkdir(jobDir, { recursive: true });
    await mkdir(path.join(jobDir, 'frames'), { recursive: true });
    
    console.log(`Created export job: ${jobId} for ${duration.toFixed(2)}s video at ${fps}fps (${width}x${height})`);
    
    // Return job ID
    return new Response(
      JSON.stringify({ 
        jobId,
        framesExpected: Math.ceil(duration * fps)
      }),
      { status: 200, headers: { 'Content-Type': 'application/json' } }
    );
  } catch (error) {
    console.error('Error creating export job:', error);
    return new Response(
      JSON.stringify({ error: `Failed to create export job: ${error}` }),
      { status: 500, headers: { 'Content-Type': 'application/json' } }
    );
  }
}