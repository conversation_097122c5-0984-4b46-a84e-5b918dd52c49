@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 224 71% 4%; /* Dark Blue/Black */
    --foreground: 210 20% 98%; /* Light Gray */

    --muted: 220 40% 10%; /* Cooler Dark Gray */
    --muted-foreground: 210 15% 80%; /* Dimmer Light Gray */

    --popover: 224 71% 4%; /* Same as background */
    --popover-foreground: 210 20% 98%; /* Same as foreground */

    --card: 220 40% 10%; /* Cooler Dark Gray */
    --card-foreground: 210 20% 98%; /* Same as foreground */

    --border: 210 25% 15%; /* Cooler Border */
    --input: 210 25% 15%; /* Cooler Input Background */

    --primary: 170 60% 45%; /* Rich <PERSON> */
    --primary-foreground: 170 100% 95%; /* Very Light Teal/White */

    --secondary: 220 35% 12%; /* Cooler Secondary */
    --secondary-foreground: 210 20% 98%; /* Light Gray */

    --accent: 170 60% 45%; /* <PERSON> */
    --accent-foreground: 170 100% 95%; /* Very Light Teal/White */

    --destructive: 0 70% 50%; /* Adjusted Dark Red */
    --destructive-foreground: 210 20% 98%; /* Light Gray */

    --ring: 170 60% 45%; /* Teal for focus */

    --radius: 0.5rem;

    /* Shadcn/ui chart colors - can be adjusted if charts are used */
    --chart-1: 170 60% 45%;
    --chart-2: 210 20% 98%;
    --chart-3: 220 40% 10%;
    --chart-4: 210 15% 80%;
    --chart-5: 210 25% 15%;
    
    /* Sidebar specific vars from existing globals, adapted */
    --sidebar-background: 224 71% 6%; /* Slightly different dark */
    --sidebar-foreground: 210 18% 90%;
    --sidebar-primary: 170 60% 45%; /* Teal for active/primary items */
    --sidebar-primary-foreground: 170 100% 95%; /* Light Teal/White text */
    --sidebar-accent: 220 40% 12%; /* Darker accent for sidebar hover */
    --sidebar-accent-foreground: 170 60% 45%; /* Teal text on hover */
    --sidebar-border: 210 25% 13%;
    --sidebar-ring: 170 60% 45%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    /* Removed font-family: Arial, Helvetica, sans-serif; as Geist is used */
  }
}
