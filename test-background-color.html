<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Color Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-container {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .preview-box, .export-box {
            width: 320px;
            height: 180px;
            border: 2px solid #333;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        .preview-box {
            background: #8B5CF6; /* Purple background like in your preview */
        }
        .export-box {
            background: #000000; /* Black background like in your export */
        }
        .skull-image {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, #ff6b6b, #ff4757);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        .color-picker {
            margin: 10px 0;
        }
        .color-picker input {
            width: 50px;
            height: 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-canvas {
            border: 1px solid #666;
            margin: 10px 0;
        }
        button {
            background: #8B5CF6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #7C3AED;
        }
        .log {
            background: #2a2a2a;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎨 Background Color Export Test</h1>
    
    <p>This test demonstrates the background color issue you're experiencing:</p>
    
    <div class="test-container">
        <div>
            <h3>Preview (Expected)</h3>
            <div class="preview-box">
                <div class="skull-image">💀</div>
            </div>
            <p>Purple background (#8B5CF6)</p>
        </div>
        
        <div>
            <h3>Export (Current Issue)</h3>
            <div class="export-box">
                <div class="skull-image">💀</div>
            </div>
            <p>Black background (#000000)</p>
        </div>
    </div>

    <h2>🧪 Canvas Background Test</h2>
    
    <div class="color-picker">
        <label>Background Color: </label>
        <input type="color" id="bgColorPicker" value="#8B5CF6">
        <span id="colorValue">#8B5CF6</span>
    </div>
    
    <canvas id="testCanvas" class="test-canvas" width="320" height="180"></canvas>
    
    <div>
        <button onclick="testCanvasBackground()">Test Canvas Background</button>
        <button onclick="testFrameRendering()">Test Frame Rendering</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="log" class="log"></div>

    <h2>🔧 Fix Verification</h2>
    <p>After applying the background color fixes:</p>
    <ul>
        <li>✅ ExportButton now accepts <code>canvasBackgroundColor</code> prop</li>
        <li>✅ Export function interface updated to include background color</li>
        <li>✅ FrameRenderer uses background color instead of hardcoded black</li>
        <li>✅ Web Worker supports background colors</li>
        <li>✅ Main app passes background color to export button</li>
    </ul>

    <script>
        const log = document.getElementById('log');
        const canvas = document.getElementById('testCanvas');
        const ctx = canvas.getContext('2d');
        const colorPicker = document.getElementById('bgColorPicker');
        const colorValue = document.getElementById('colorValue');

        function logMessage(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            log.innerHTML = '';
        }

        colorPicker.addEventListener('change', (e) => {
            colorValue.textContent = e.target.value;
        });

        function testCanvasBackground() {
            const bgColor = colorPicker.value;
            logMessage(`Testing canvas background with color: ${bgColor}`);
            
            // Clear and set background
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw a test skull
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, 30, 0, 2 * Math.PI);
            ctx.fill();
            
            // Add text
            ctx.fillStyle = 'white';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('💀', canvas.width / 2, canvas.height / 2 + 7);
            
            logMessage(`✅ Canvas background set to ${bgColor}`);
        }

        function testFrameRendering() {
            logMessage('🧪 Testing frame rendering simulation...');
            
            // Simulate the export process
            const bgColor = colorPicker.value;
            
            // Step 1: Clear with background color (like in export)
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            logMessage(`Step 1: Set background to ${bgColor}`);
            
            // Step 2: Draw visual media (simulated)
            ctx.fillStyle = '#ff6b6b';
            ctx.beginPath();
            ctx.arc(canvas.width / 2, canvas.height / 2, 40, 0, 2 * Math.PI);
            ctx.fill();
            logMessage('Step 2: Draw visual media');
            
            // Step 3: Apply effects (simulated pulse)
            const scale = 1.2; // Simulated audio energy effect
            ctx.save();
            ctx.translate(canvas.width / 2, canvas.height / 2);
            ctx.scale(scale, scale);
            ctx.translate(-canvas.width / 2, -canvas.height / 2);
            
            ctx.fillStyle = 'white';
            ctx.font = '24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('💀', canvas.width / 2, canvas.height / 2 + 8);
            
            ctx.restore();
            logMessage('Step 3: Apply pulse effect');
            
            logMessage('✅ Frame rendering test complete');
        }

        // Initialize
        logMessage('🚀 Background Color Test initialized');
        logMessage('The issue: Export uses black background instead of selected color');
        logMessage('Expected: Export should match preview background color');
        
        // Test initial canvas
        testCanvasBackground();
    </script>
</body>
</html>
