/**
 * Calculates the Root Mean Square (RMS) of an audio segment
 * RMS is a good measure of the average power of an audio signal
 */
export function calculateRMS(audioSegment: Float32Array): number {
  if (audioSegment.length === 0) return 0;
  let sumOfSquares = 0;
  for (const sample of audioSegment) {
    sumOfSquares += sample * sample;
  }
  return Math.sqrt(sumOfSquares / audioSegment.length);
}

/**
 * Gets the normalized audio energy at a specific time from an AudioBuffer
 * Returns a value between 0 and 1 representing the energy level
 */
export function getAudioEnergyAtTime(
  audioBuffer: AudioBuffer,
  time: number,
  windowDuration: number = 0.05,
  normalizationFactor: number = 4.0
): number {
  const sampleRate = audioBuffer.sampleRate;
  const startSample = Math.floor(time * sampleRate);
  const windowSamples = Math.floor(windowDuration * sampleRate);
  const endSample = startSample + windowSamples;

  if (startSample >= audioBuffer.length) return 0;

  const channelData = audioBuffer.getChannelData(0); // Use first channel
  const actualEndSample = Math.min(endSample, audioBuffer.length);
  const segment = channelData.slice(startSample, actualEndSample);

  const rms = calculateRMS(segment);
  // Normalize RMS to a 0-1 range
  return Math.min(rms * normalizationFactor, 1.0);
}