"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>ha<PERSON> } from "lucide-react";

export default function ElementsPanel() {
  return (
    <Card className="h-full overflow-y-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Shapes className="mr-2 h-6 w-6 text-accent" />
          Elements
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">
          Add and customize visual elements like particles, shapes, and overlays. (Coming Soon)
        </p>
      </CardContent>
    </Card>
  );
}
