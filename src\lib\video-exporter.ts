// src/lib/video-exporter.ts
'use client';

const ANALYSIS_WINDOW_DURATION_SEC = 0.05; // 50ms
const PULSE_MAX_SCALE_INCREASE = 0.3; // e.g., visual scale from 1.0 to 1.3
const RMS_NORMALIZATION_BOOST = 4.0; // Empirical value to boost RMS to a 0-1 range for pulse

interface ExportVideoParams {
  songDataUri: string;
  imageSrc: string;
  outputWidth: number;
  outputHeight: number;
  fps: number;
  onProgress: (progress: number) => void;
}

// Helper to calculate RMS of an audio segment
function calculateRMS(audioSegment: Float32Array): number {
  if (audioSegment.length === 0) return 0;
  let sumOfSquares = 0;
  for (const sample of audioSegment) {
    sumOfSquares += sample * sample;
  }
  return Math.sqrt(sumOfSquares / audioSegment.length);
}

// Helper to get normalized audio energy at a specific time from an AudioBuffer
function getAudioEnergyAtTime(
  audioBuffer: AudioBuffer,
  time: number,
  windowDuration: number
): number {
  const sampleRate = audioBuffer.sampleRate;
  const startSample = Math.floor(time * sampleRate);
  const windowSamples = Math.floor(windowDuration * sampleRate);
  const endSample = startSample + windowSamples;

  if (startSample >= audioBuffer.length) return 0;

  const channelData = audioBuffer.getChannelData(0); // Use first channel
  const actualEndSample = Math.min(endSample, audioBuffer.length);
  const segment = channelData.slice(startSample, actualEndSample);

  const rms = calculateRMS(segment);
  // Normalize RMS. This is a simplified normalization.
  return Math.min(rms * RMS_NORMALIZATION_BOOST, 1.0);
}


export async function exportToWebM({
  songDataUri,
  imageSrc,
  outputWidth,
  outputHeight,
  fps,
  onProgress,
}: ExportVideoParams): Promise<Blob> {
  return new Promise(async (resolve, reject) => {
    let audioContext: AudioContext | null = null;
    try {
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();

      // 1. Load Audio
      onProgress(0.01); 
      const audioResponse = await fetch(songDataUri);
      const audioArrayBuffer = await audioResponse.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(audioArrayBuffer);
      const audioDuration = audioBuffer.duration;

      // 2. Load Image
      onProgress(0.05);
      const image = new Image();
      image.crossOrigin = "anonymous"; 
      const imageLoadPromise = new Promise<void>((resolveLoad, rejectLoad) => {
        image.onload = () => resolveLoad();
        image.onerror = (err) => rejectLoad(new Error(`Failed to load image: ${err instanceof ErrorEvent ? err.message : String(err)}`));
      });
      image.src = imageSrc;
      await imageLoadPromise;

      // 3. Canvas Setup
      const canvas = document.createElement('canvas');
      canvas.width = outputWidth;
      canvas.height = outputHeight;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get 2D context from canvas');
      }

      // 4. MediaRecorder Setup
      const videoStream = canvas.captureStream(fps);
      
      const audioSourceNode = audioContext.createBufferSource();
      audioSourceNode.buffer = audioBuffer;
      const mediaStreamDestination = audioContext.createMediaStreamDestination();
      audioSourceNode.connect(mediaStreamDestination);
      // audioSourceNode.connect(audioContext.destination); // Optional: to hear audio during export
      
      const audioStreamForRecording = mediaStreamDestination.stream;

      const combinedStream = new MediaStream([
        ...videoStream.getVideoTracks(),
        ...audioStreamForRecording.getAudioTracks(),
      ]);
      
      const PREFERRED_MIME_TYPE = 'video/webm; codecs="vp9, opus"';
      const FALLBACK_MIME_TYPE = 'video/webm';
      let mimeTypeToUse = FALLBACK_MIME_TYPE;

      if (MediaRecorder.isTypeSupported(PREFERRED_MIME_TYPE)) {
        mimeTypeToUse = PREFERRED_MIME_TYPE;
      } else if (!MediaRecorder.isTypeSupported(FALLBACK_MIME_TYPE)) {
        throw new Error('video/webm is not supported by MediaRecorder');
      }
      console.log(`Using mimeType: ${mimeTypeToUse}`);


      const recorder = new MediaRecorder(combinedStream, { mimeType: mimeTypeToUse });

      const recordedChunks: BlobPart[] = [];
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
        }
      };

      recorder.onstop = () => {
        const fullBlob = new Blob(recordedChunks, { type: mimeTypeToUse });
        if (audioContext && audioContext.state !== 'closed') {
            audioContext.close().catch(e => console.error("Error closing audio context onstop:", e));
        }
        resolve(fullBlob);
      };
      
      recorder.onerror = (event) => {
        console.error("MediaRecorder error:", event);
         if (audioContext && audioContext.state !== 'closed') {
            audioContext.close().catch(e => console.error("Error closing audio context onerror:", e));
        }
        reject(new Error(`MediaRecorder error: ${ (event as any)?.error?.name || 'Unknown error'}`));
      };

      // 5. Rendering Loop
      recorder.start();
      audioSourceNode.start(); 

      const numFrames = Math.floor(audioDuration * fps);
      for (let i = 0; i < numFrames; i++) {
        const currentTime = i / fps;

        if (audioContext.state === 'suspended') {
            await audioContext.resume();
        }

        const audioEnergy = getAudioEnergyAtTime(audioBuffer, currentTime, ANALYSIS_WINDOW_DURATION_SEC);
        const scale = 1.0 + audioEnergy * PULSE_MAX_SCALE_INCREASE;

        ctx.fillStyle = 'black'; 
        ctx.fillRect(0, 0, outputWidth, outputHeight);

        const imgAspectRatio = image.naturalWidth / image.naturalHeight;
        const canvasAspectRatio = outputWidth / outputHeight;
        
        let drawWidth, drawHeight, offsetX, offsetY;

        // Maintain aspect ratio of image, fit within canvas, then apply scale from center
        let baseDrawWidth, baseDrawHeight;
        if (imgAspectRatio > canvasAspectRatio) { // Image wider than canvas aspect ratio (letterbox)
            baseDrawWidth = outputWidth;
            baseDrawHeight = outputWidth / imgAspectRatio;
        } else { // Image taller or same aspect (pillarbox)
            baseDrawHeight = outputHeight;
            baseDrawWidth = outputHeight * imgAspectRatio;
        }

        drawWidth = baseDrawWidth * scale;
        drawHeight = baseDrawHeight * scale;
        
        offsetX = (outputWidth - drawWidth) / 2;
        offsetY = (outputHeight - drawHeight) / 2;

        ctx.drawImage(image, offsetX, offsetY, drawWidth, drawHeight);
        
        if (i % Math.max(1, Math.floor(numFrames / 100)) === 0 || i === numFrames - 1) {
           onProgress( (i + 1) / numFrames);
        }
        
        // Artificial delay to simulate frame rate for captureStream
        await new Promise(r => setTimeout(r, (1000 / fps) * 0.9)); // Slightly less than frame duration
      }

      // 6. Finalize
      if (audioSourceNode) audioSourceNode.stop();
      if (recorder.state === "recording") {
         recorder.stop();
      } else if (recorder.state === "paused") {
         recorder.stop(); // Or resume and then stop, depending on desired behavior for paused state
      }
      // onstop will handle resolve/reject
    } catch (error) {
      console.error("Export failed:", error);
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close().catch(e => console.error("Error closing audio context on catch:", e));
      }
      reject(error);
    }
  });
}

/**
 * Highly optimized version of exportToWebM with perfect audio-visual synchronization
 */
export async function exportToWebMOptimized({
  songDataUri,
  imageSrc,
  outputWidth,
  outputHeight,
  fps,
  onProgress,
}: ExportVideoParams): Promise<Blob> {
  return new Promise(async (resolve, reject) => {
    let audioContext: AudioContext | null = null;
    let audioSourceNode: AudioBufferSourceNode | null = null;
    let recorder: MediaRecorder | null = null;
    let animationFrameId: number | null = null;
    
    // Comprehensive cleanup function
    const cleanup = () => {
      console.log("[Exporter] Cleanup initiated");
      
      // Cancel any pending animation frames
      if (animationFrameId !== null) {
        window.cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
      }
      
      // Stop and disconnect audio source
      if (audioSourceNode) {
        try {
          // Check if audio source is active before stopping
          try {
            audioSourceNode.stop();
          } catch (e) {
            // Ignore errors if already stopped
          }
          audioSourceNode.disconnect();
          console.log("[Exporter] Audio source stopped and disconnected");
        } catch (e) {
          console.warn("[Exporter] Error stopping audio source:", e);
        }
      }
      
      // Stop recorder if still recording
      if (recorder) {
        try {
          if (recorder.state === "recording") {
            recorder.stop();
            console.log("[Exporter] Recorder stopped");
          }
        } catch (e) {
          console.warn("[Exporter] Error stopping recorder:", e);
        }
      }
      
      // Close audio context
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close().catch(e => console.error("[Exporter] Error closing audio context:", e));
        console.log("[Exporter] Audio context closed");
      }
      
      console.log("[Exporter] Cleanup completed");
    };
    
    try {
      // 1. Initialize AudioContext with high-quality settings
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        latencyHint: 'interactive',
        sampleRate: 48000
      });
      console.log("[Exporter] AudioContext created with sample rate:", audioContext.sampleRate);
      onProgress(0.01);
      
      // 2. Load and decode audio with high precision
      console.log("[Exporter] Loading audio...");
      const audioResponse = await fetch(songDataUri);
      const audioArrayBuffer = await audioResponse.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(audioArrayBuffer);
      const audioDuration = audioBuffer.duration;
      console.log(`[Exporter] Audio decoded. Duration: ${audioDuration.toFixed(3)}s, Sample rate: ${audioBuffer.sampleRate}Hz, Channels: ${audioBuffer.numberOfChannels}`);
      onProgress(0.1);
      
      // 3. Load image with proper error handling
      console.log("[Exporter] Loading image...");
      const image = new Image();
      image.crossOrigin = "anonymous";
      await new Promise<void>((resolveLoad, rejectLoad) => {
        image.onload = () => {
          console.log(`[Exporter] Image loaded. Dimensions: ${image.naturalWidth}x${image.naturalHeight}`);
          resolveLoad();
        };
        image.onerror = (err) => {
          console.error("[Exporter] Image load error:", err);
          rejectLoad(new Error(`Failed to load image: ${err instanceof ErrorEvent ? err.message : String(err)}`));
        };
        image.src = imageSrc;
      });
      onProgress(0.15);
      
      // 4. Set up canvas with optimized settings
      const canvas = document.createElement('canvas');
      canvas.width = outputWidth;
      canvas.height = outputHeight;
      // Use optimized context settings: no alpha for better performance
      const ctx = canvas.getContext('2d', {
        alpha: false,
        desynchronized: true,
        willReadFrequently: false
      });
      if (!ctx) {
        throw new Error('Failed to get 2D context from canvas');
      }
      
      // Pre-calculate image dimensions once (optimization)
      const imgAspectRatio = image.naturalWidth / image.naturalHeight;
      const canvasAspectRatio = outputWidth / outputHeight;
      
      let baseDrawWidth: number, baseDrawHeight: number;
      if (imgAspectRatio > canvasAspectRatio) {
        baseDrawWidth = outputWidth;
        baseDrawHeight = outputWidth / imgAspectRatio;
      } else {
        baseDrawHeight = outputHeight;
        baseDrawWidth = outputHeight * imgAspectRatio;
      }
      
      // Define frame data type with all necessary information
      interface FrameData {
        time: number;
        audioEnergy: number;
      }
      
      // 5. Pre-calculate all audio energy data for precise timing
      console.log("[Exporter] Pre-calculating audio energy data...");
      const numFrames = Math.ceil(audioDuration * fps);
      const frameData: FrameData[] = [];
      
      for (let i = 0; i < numFrames; i++) {
        const frameTime = i / fps;
        const audioEnergy = getAudioEnergyAtTime(audioBuffer, frameTime, ANALYSIS_WINDOW_DURATION_SEC);
        
        frameData.push({
          time: frameTime,
          audioEnergy
        });
        
        if (i % Math.max(1, Math.floor(numFrames / 10)) === 0) {
          onProgress(0.15 + (i / numFrames) * 0.15);
        }
      }
      console.log(`[Exporter] Audio energy data calculated for ${numFrames} frames`);
      onProgress(0.3);
      
      // 6. Set up MediaRecorder with optimized settings
      console.log("[Exporter] Setting up MediaRecorder...");
      // Request higher frame rate for better quality
      const videoStream = canvas.captureStream(fps);
      
      audioSourceNode = audioContext.createBufferSource();
      audioSourceNode.buffer = audioBuffer;
      const mediaStreamDestination = audioContext.createMediaStreamDestination();
      audioSourceNode.connect(mediaStreamDestination);
      
      const combinedStream = new MediaStream([
        ...videoStream.getVideoTracks(),
        ...mediaStreamDestination.stream.getAudioTracks()
      ]);
      
      // Find best supported MIME type with highest quality codecs
      const mimeTypes = [
        'video/webm; codecs="vp9, opus"',
        'video/webm; codecs="vp8, opus"',
        'video/webm; codecs="vp9"',
        'video/webm; codecs="vp8"',
        'video/webm'
      ];
      
      let mimeTypeToUse = '';
      for (const type of mimeTypes) {
        if (MediaRecorder.isTypeSupported(type)) {
          mimeTypeToUse = type;
          console.log(`[Exporter] Found supported MIME type: ${type}`);
          break;
        }
      }
      
      if (!mimeTypeToUse) {
        throw new Error('No supported MIME type found for MediaRecorder');
      }
      
      console.log(`[Exporter] Using MIME type: ${mimeTypeToUse}`);
      
      const recordedChunks: BlobPart[] = [];
      recorder = new MediaRecorder(combinedStream, {
        mimeType: mimeTypeToUse,
        videoBitsPerSecond: 8000000, // 8 Mbps for higher quality
        audioBitsPerSecond: 192000   // 192 kbps for better audio
      });
      
      // Request data chunks every second for better memory management
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
          console.log(`[Exporter] Recorded chunk: ${(event.data.size / 1024 / 1024).toFixed(2)} MB`);
        }
      };
      
      recorder.onstop = () => {
        console.log("[Exporter] Recorder stopped, creating final video...");
        const fullBlob = new Blob(recordedChunks, { type: mimeTypeToUse });
        console.log(`[Exporter] Final video size: ${(fullBlob.size / 1024 / 1024).toFixed(2)} MB`);
        cleanup();
        resolve(fullBlob);
      };
      
      recorder.onerror = (event) => {
        console.error("[Exporter] MediaRecorder error:", event);
        cleanup();
        reject(new Error(`MediaRecorder error: ${(event as any)?.error?.name || 'Unknown error'}`));
      };
      
      // 7. Start recording with timeslice for chunked recording
      console.log("[Exporter] Starting recording process...");
      recorder.start(1000); // Collect data in 1-second chunks
      
      // 8. Precise rendering with frame-perfect timing
      console.log("[Exporter] Beginning frame rendering...");
      
      // Start audio at exactly the same time as our animation
      const startTime = audioContext.currentTime;
      audioSourceNode.start(startTime);
      console.log(`[Exporter] Audio started at context time: ${startTime}`);
      onProgress(0.35);
      
      let lastFrameIndex = -1;
      
      const renderFrame = () => {
        // Calculate exact time since start (with null check)
        const elapsedTime = audioContext ? audioContext.currentTime - startTime : 0;
        
        // Determine which frame we should be on
        const currentFrameIndex = Math.min(
          Math.floor(elapsedTime * fps),
          frameData.length - 1
        );
        
        // Only render if we've moved to a new frame
        if (currentFrameIndex > lastFrameIndex) {
          // Get the frame data for this exact moment
          const frame = frameData[currentFrameIndex];
          
          // Calculate scale based on audio energy
          const scale = 1.0 + frame.audioEnergy * PULSE_MAX_SCALE_INCREASE;
          
          // Calculate dimensions for this frame
          const drawWidth = baseDrawWidth * scale;
          const drawHeight = baseDrawHeight * scale;
          const offsetX = (outputWidth - drawWidth) / 2;
          const offsetY = (outputHeight - drawHeight) / 2;
          
          // Clear canvas with black background
          ctx.fillStyle = 'black';
          ctx.fillRect(0, 0, outputWidth, outputHeight);
          
          // Draw image with pulse effect
          ctx.drawImage(
            image,
            offsetX,
            offsetY,
            drawWidth,
            drawHeight
          );
          
          // Update last frame index
          lastFrameIndex = currentFrameIndex;
          
          // Update progress periodically
          if (currentFrameIndex % Math.max(1, Math.floor(frameData.length / 20)) === 0) {
            const progress = 0.35 + (currentFrameIndex / frameData.length) * 0.6;
            onProgress(progress);
            console.log(`[Exporter] Rendering progress: ${(progress * 100).toFixed(0)}%, Frame: ${currentFrameIndex}/${frameData.length}, Time: ${elapsedTime.toFixed(2)}s`);
          }
        }
        
        // Check if we've reached the end
        if (elapsedTime >= audioDuration) {
          console.log("[Exporter] Rendering complete, finalizing video...");
          onProgress(0.95);
          
          // Allow a small buffer time for the recorder to capture the last frame
          setTimeout(() => {
            if (recorder && recorder.state === "recording") {
              recorder.stop();
            }
          }, 100);
          
          return;
        }
        
        // Continue rendering
        animationFrameId = requestAnimationFrame(renderFrame);
      };
      
      // Start the rendering loop
      animationFrameId = requestAnimationFrame(renderFrame);
      
    } catch (error) {
      console.error("[Exporter] Export failed:", error);
      cleanup();
      reject(error);
    }
  });
}
