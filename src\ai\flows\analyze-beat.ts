'use server';

/**
 * @fileOverview Analyzes the beat and rhythm of an uploaded song.
 *
 * - analyzeBeat - A function that handles the beat analysis process.
 * - AnalyzeBeatInput - The input type for the analyzeBeat function.
 * - AnalyzeBeatOutput - The return type for the analyzeBeat function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnalyzeBeatInputSchema = z.object({
  audioDataUri: z
    .string()
    .describe(
      'An audio file, as a data URI that must include a MIME type and use Base64 encoding. Expected format: data:<mimetype>;base64,<encoded_data>.'
    ),
});
export type AnalyzeBeatInput = z.infer<typeof AnalyzeBeatInputSchema>;

const AnalyzeBeatOutputSchema = z.object({
  beats: z.array(
    z.number().describe('The timestamps (in seconds) of the detected beats.')
  ).
describe('A list of timestamps representing the detected beats in the song.'),
  tempo: z.number().describe('The estimated tempo of the song in BPM (beats per minute).'),
});
export type AnalyzeBeatOutput = z.infer<typeof AnalyzeBeatOutputSchema>;

export async function analyzeBeat(input: AnalyzeBeatInput): Promise<AnalyzeBeatOutput> {
  return analyzeBeatFlow(input);
}

const analyzeBeatPrompt = ai.definePrompt({
  name: 'analyzeBeatPrompt',
  input: {schema: AnalyzeBeatInputSchema},
  output: {schema: AnalyzeBeatOutputSchema},
  prompt: `You are an AI expert in music analysis. Your task is to analyze the beat and rhythm of a song provided as an audio file.

  Analyze the following audio file to identify the timestamps of the beats and estimate the tempo (BPM).
  Audio: {{media url=audioDataUri}}
  
  Return the detected beats as a list of timestamps (in seconds) and the estimated tempo in BPM.
  Be as accurate as possible.`,
});

const analyzeBeatFlow = ai.defineFlow(
  {
    name: 'analyzeBeatFlow',
    inputSchema: AnalyzeBeatInputSchema,
    outputSchema: AnalyzeBeatOutputSchema,
  },
  async input => {
    const {output} = await analyzeBeatPrompt(input);
    return output!;
  }
);
