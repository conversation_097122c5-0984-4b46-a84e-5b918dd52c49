
// src/lib/video-exporter-mp4.ts
'use client';

// Constants for pulse effect - can be tuned
const ANALYSIS_WINDOW_DURATION_SEC = 0.05; // 50ms window for audio analysis
const PULSE_MAX_SCALE_INCREASE = 0.3;    // e.g., visual scale from 1.0 to 1.3
const RMS_NORMALIZATION_BOOST = 4.0;     // Empirical value to boost RMS for pulse effect

// Suggested bitrates for 720p output
const VIDEO_BITRATE = 2500000; // 2.5 Mbps
const AUDIO_BITRATE = 128000;  // 128 Kbps

interface ExportMp4Params {
  songDataUri: string;
  visualMediaSrc: string;
  visualMediaType: "image" | "video";
  outputWidth: number;
  outputHeight: number;
  fps: number;
  onProgress: (progress: number, info?: string) => void;
}

interface ExportResult {
  blob: Blob;
  actualMimeType: string;
}

// Helper to calculate RMS of an audio segment
function calculateRMS(audioSegment: Float32Array): number {
  if (audioSegment.length === 0) return 0;
  let sumOfSquares = 0;
  for (const sample of audioSegment) {
    sumOfSquares += sample * sample;
  }
  return Math.sqrt(sumOfSquares / audioSegment.length);
}

// Helper to get normalized audio energy at a specific time from an AudioBuffer
function getAudioEnergyAtTime(
  audioBuffer: AudioBuffer,
  time: number,
  windowDuration: number
): number {
  const sampleRate = audioBuffer.sampleRate;
  const startSample = Math.floor(time * sampleRate);
  const windowSamples = Math.floor(windowDuration * sampleRate);
  const endSample = startSample + windowSamples;

  if (startSample >= audioBuffer.length) return 0;

  const channelData = audioBuffer.getChannelData(0); // Use first channel
  const actualEndSample = Math.min(endSample, audioBuffer.length);
  const segment = channelData.slice(startSample, actualEndSample);

  const rms = calculateRMS(segment);
  return Math.min(rms * RMS_NORMALIZATION_BOOST, 1.0); // Normalize and boost
}

export async function exportToMp4({
  songDataUri,
  visualMediaSrc,
  visualMediaType,
  outputWidth,
  outputHeight,
  fps,
  onProgress,
}: ExportMp4Params): Promise<ExportResult> {
  console.log(`[Exporter] Starting export. SongDataUri length: ${songDataUri?.length}, VisualMediaSrc length: ${visualMediaSrc?.length}, VisualMediaType: ${visualMediaType}`);
  onProgress(0.0, "Initializing export...");

  return new Promise(async (resolve, reject) => {
    let audioContext: AudioContext | null = null;
    let visualVideoElement: HTMLVideoElement | null = null;
    let audioSourceNode: AudioBufferSourceNode | null = null;
    let recorder: MediaRecorder | null = null;
    const recordedChunks: BlobPart[] = [];
    let mimeTypeToUse = '';
    let audioBufferForExport: AudioBuffer | null = null;

    const cleanup = () => {
      console.log("[Exporter] Cleanup initiated.");
      if (audioSourceNode) {
        try {
          if (audioSourceNode.playbackState === audioSourceNode.PLAYING_STATE || audioSourceNode.playbackState === audioSourceNode.SCHEDULED_STATE) {
            audioSourceNode.stop();
            console.log("[Exporter] AudioSourceNode stopped in cleanup.");
          }
          audioSourceNode.onended = null; 
          audioSourceNode.disconnect();
          console.log("[Exporter] AudioSourceNode disconnected.");
        } catch (e) {
          console.error("[Exporter] Error stopping/disconnecting AudioSourceNode:", e);
        }
      }
      if (recorder && (recorder.state === "recording" || recorder.state === "paused")) {
        recorder.onstop = null; // Prevent double handling if already stopping
        recorder.ondataavailable = null;
        recorder.onerror = null;
        try {
          recorder.stop(); // Attempt to stop if not already
          console.log("[Exporter] MediaRecorder stopped in cleanup.");
        } catch(e) {
          console.error("[Exporter] Error stopping MediaRecorder in cleanup:", e);
        }
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close().catch(e => console.error("[Exporter] Error closing audio context on cleanup:", e));
        console.log("[Exporter] AudioContext closed.");
      }
      if (visualVideoElement) {
        if (!visualVideoElement.paused) {
          visualVideoElement.pause();
        }
        visualVideoElement.src = ''; 
        visualVideoElement.load(); 
        console.log("[Exporter] Visual video element cleaned up.");
      }
      console.log("[Exporter] Cleanup finished.");
    };

    try {
      audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      console.log(`[Exporter] AudioContext created. State: ${audioContext.state}`);
      onProgress(0.01, "AudioContext created.");

      // 1. Load Audio
      console.log("[Exporter] Fetching audio data...");
      const audioResponse = await fetch(songDataUri);
      const audioArrayBuffer = await audioResponse.arrayBuffer();
      console.log(`[Exporter] Audio data fetched (${audioArrayBuffer.byteLength} bytes). Decoding...`);
      onProgress(0.03, "Decoding audio...");
      audioBufferForExport = await audioContext.decodeAudioData(audioArrayBuffer.slice(0));
      
      const audioDuration = audioBufferForExport.duration;
      console.log(`[Exporter] Audio decoded. Nominal duration: ${audioDuration.toFixed(3)}s. Sample rate: ${audioBufferForExport.sampleRate}Hz. Channels: ${audioBufferForExport.numberOfChannels}.`);
      onProgress(0.05, `Audio decoded (${audioDuration.toFixed(1)}s)`);

      if (audioDuration <= 0 || !isFinite(audioDuration)) {
        reject(new Error(`Audio duration is invalid (${audioDuration}). Cannot export.`));
        cleanup();
        return;
      }
      const numFrames = Math.floor(audioDuration * fps);
      if (numFrames <= 0) {
        reject(new Error(`Calculated number of frames is ${numFrames}. Audio duration might be too short or FPS too low.`));
        cleanup();
        return;
      }
      console.log(`[Exporter] Target total frames: ${numFrames} for ${audioDuration.toFixed(3)}s at ${fps}fps.`);

      // 2. Load Visual Media
      onProgress(0.06, "Loading visual media...");
      let visualMedia: HTMLImageElement | HTMLVideoElement;
      if (visualMediaType === "image") {
        const image = new Image();
        image.crossOrigin = "anonymous";
        const imageLoadPromise = new Promise<void>((resolveLoad, rejectLoad) => {
          image.onload = () => resolveLoad();
          image.onerror = (err) => rejectLoad(new Error(`Failed to load image: ${err instanceof ErrorEvent ? err.message : String(err)}`));
        });
        image.src = visualMediaSrc;
        await imageLoadPromise;
        visualMedia = image;
        console.log("[Exporter] Visual image loaded.");
      } else { 
        visualVideoElement = document.createElement('video');
        visualVideoElement.crossOrigin = "anonymous";
        visualVideoElement.muted = true; 
        visualVideoElement.loop = true; 
        
        const videoLoadPromise = new Promise<void>((resolveLoad, rejectLoad) => {
          visualVideoElement!.onloadedmetadata = () => { 
             if (visualVideoElement!.duration > 0) {
                console.log(`[Exporter] Visual video loaded. Duration: ${visualVideoElement!.duration}s`);
                resolveLoad();
             } else { // Fallback for videos that might not fire loadedmetadata with duration immediately
                visualVideoElement!.oncanplay = () => {
                    if (visualVideoElement!.duration > 0) { // Check duration again on canplay
                       console.log(`[Exporter] Visual video canplay. Duration: ${visualVideoElement!.duration}s`);
                       resolveLoad();
                    } else {
                       console.warn("[Exporter] Visual video canplay but duration is still 0. Attempting to proceed with caution.");
                       resolveLoad(); // Proceed but log, might be an issue with the video file or browser.
                    }
                };
             }
          };
          visualVideoElement!.onerror = (err) => rejectLoad(new Error(`Failed to load video: ${visualVideoElement?.error?.message || String(err)}`));
        });
        visualVideoElement.src = visualMediaSrc;
        await videoLoadPromise;
        visualMedia = visualVideoElement;
        
        try {
            // Ensure video is ready to play for frame grabbing
            if (visualVideoElement.paused) {
              await visualVideoElement.play(); 
            }
            // For videos, it's good to ensure it's at the beginning if we want to use it from start
            // visualVideoElement.currentTime = 0; // Not strictly necessary if looping from current frameTime
            console.log("[Exporter] Visual video playback started/ensured for export rendering.");
        } catch (playError) {
            console.warn("[Exporter] Could not start visual video playback for export, attempting to draw from current state:", playError);
        }
      }
      onProgress(0.10, "Visual media loaded.");

      // 3. Canvas Setup
      const canvas = document.createElement('canvas');
      canvas.width = outputWidth;
      canvas.height = outputHeight;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get 2D context from canvas');
      }
      console.log(`[Exporter] Canvas created (${outputWidth}x${outputHeight}).`);

      // 4. MediaRecorder Setup
      const videoStreamFromCanvas = canvas.captureStream(fps);
      console.log(`[Exporter] Canvas stream captured at ${fps}fps. Video track ready state: ${videoStreamFromCanvas.getVideoTracks()[0].readyState}`);
      
      audioSourceNode = audioContext.createBufferSource();
      audioSourceNode.buffer = audioBufferForExport;
      const mediaStreamDestination = audioContext.createMediaStreamDestination();
      audioSourceNode.connect(mediaStreamDestination);
      console.log("[Exporter] AudioSourceNode created and connected to MediaStreamDestination.");
      
      audioSourceNode.onended = () => {
        console.error(`[Exporter] CRITICAL: AudioSourceNode 'ended' event fired. AudioContext currentTime: ${audioContext?.currentTime.toFixed(3)}, Expected AudioBuffer duration: ${audioBufferForExport?.duration.toFixed(3)}`);
      };
      
      const audioStreamForRecording = mediaStreamDestination.stream;
      const audioTrackForRecording = audioStreamForRecording.getAudioTracks()[0];
      if (audioTrackForRecording) {
        console.log(`[Exporter] Audio track for recording obtained. Ready state: ${audioTrackForRecording.readyState}`);
        audioTrackForRecording.onended = () => {
            console.error("[Exporter] CRITICAL: The audio track provided TO MediaRecorder has ended.");
        };
      } else {
        console.error("[Exporter] CRITICAL: No audio track obtained for MediaRecorder.");
        reject(new Error("Failed to obtain audio track for MediaRecorder."));
        cleanup();
        return;
      }

      const combinedStream = new MediaStream([
        ...videoStreamFromCanvas.getVideoTracks(),
        audioTrackForRecording, 
      ]);
      console.log(`[Exporter] Combined stream created. Video tracks: ${combinedStream.getVideoTracks().length}, Audio tracks: ${combinedStream.getAudioTracks().length}`);
      
      const mimeTypesToTry: { type: string, extension: string }[] = [
        { type: 'video/webm; codecs="vp9, opus"', extension: 'webm' },
        { type: 'video/mp4; codecs="avc1.4d002a, mp4a.40.2"', extension: 'mp4'}, 
        { type: 'video/webm; codecs="vp8, opus"', extension: 'webm' }, 
        { type: 'video/mp4; codecs="avc1.42E01E, mp4a.40.2"', extension: 'mp4' }, 
        { type: 'video/webm', extension: 'webm' },
        { type: 'video/mp4', extension: 'mp4' },
      ];

      for (const mimeInfo of mimeTypesToTry) {
        if (MediaRecorder.isTypeSupported(mimeInfo.type)) {
          mimeTypeToUse = mimeInfo.type;
          break;
        }
      }

      if (!mimeTypeToUse) {
        throw new Error('No supported MIME type (WebM or MP4) found for MediaRecorder.');
      }
      console.log(`[Exporter] MediaRecorder will use mimeType: ${mimeTypeToUse}`);

      recorder = new MediaRecorder(combinedStream, { 
        mimeType: mimeTypeToUse,
        videoBitsPerSecond: VIDEO_BITRATE,
        audioBitsPerSecond: AUDIO_BITRATE,
      });
      console.log(`[Exporter] MediaRecorder instantiated. Initial state: ${recorder.state}`);
      
      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunks.push(event.data);
        } else {
          console.warn("[Exporter] MediaRecorder.ondataavailable called with empty data chunk.");
        }
      };

      recorder.onstop = () => {
        console.log(`[Exporter] MediaRecorder 'stop' event. Recorded chunks: ${recordedChunks.length}. AudioContext state: ${audioContext?.state}, currentTime: ${audioContext?.currentTime.toFixed(3)}`);
        if (recordedChunks.length === 0) {
            console.error("[Exporter] MediaRecorder stopped with no data chunks. This indicates a recording failure.");
            reject(new Error("Recording failed: No data was captured. Check console for MediaRecorder errors or premature stream endings."));
        } else {
            const fullBlob = new Blob(recordedChunks, { type: mimeTypeToUse });
            console.log(`[Exporter] Recording stopped. Total Blob size: ${fullBlob.size}, type: ${fullBlob.type}. Number of chunks: ${recordedChunks.length}`);
            onProgress(1.0, "Export complete!");
            resolve({ blob: fullBlob, actualMimeType: mimeTypeToUse });
        }
        cleanup();
      };
      
      recorder.onerror = (event) => {
        const error = (event as any).error || new Error('Unknown MediaRecorder error');
        console.error("[Exporter] MediaRecorder error:", error.name, error.message, event);
        reject(new Error(`MediaRecorder error: ${error.name} - ${error.message}`));
        cleanup();
      };

      // 5. Rendering Loop
      console.log(`[Exporter] Starting MediaRecorder (state: ${recorder.state}) and AudioSourceNode (duration: ${audioBufferForExport.duration.toFixed(3)}s)`);
      recorder.start(); 
      audioSourceNode.start(); 
      console.log(`[Exporter] Recorder (state: ${recorder.state}) and audio source started.`);

      const renderFrames = async () => {
        console.log(`[Exporter] Starting renderFrames loop for ${numFrames} frames.`);
        for (let i = 0; i < numFrames; i++) {
          const frameTime = i / fps; 

          if (audioContext?.state === 'closed') { 
            console.error("[Exporter] AudioContext closed unexpectedly during frame rendering. Frame:", i);
            throw new Error("AudioContext closed unexpectedly during frame rendering.");
          }
          if (audioContext?.state === 'suspended') {
              console.warn(`[Exporter] AudioContext suspended at frame ${i}, time ${frameTime.toFixed(3)}s. Attempting to resume.`);
              await audioContext.resume();
              console.log(`[Exporter] AudioContext resumed. State: ${audioContext.state}`);
          }
          
          if (i % (fps * 5) === 0 || i === numFrames - 1) { 
            const progressPercent = 0.10 + (0.85 * ((i + 1) / numFrames));
            const progressInfo = `Rendering frame ${i+1}/${numFrames}. Target time: ${frameTime.toFixed(2)}s. AC_Time: ${audioContext?.currentTime.toFixed(2)}s. AudioTrackState: ${audioTrackForRecording?.readyState}`;
            console.log(`[Exporter] ${progressInfo}`);
            onProgress(progressPercent, progressInfo);
          }

          const audioEnergy = getAudioEnergyAtTime(audioBufferForExport!, frameTime, ANALYSIS_WINDOW_DURATION_SEC);
          const scale = 1.0 + audioEnergy * PULSE_MAX_SCALE_INCREASE;

          ctx.fillStyle = 'black'; 
          ctx.fillRect(0, 0, outputWidth, outputHeight);

          let mediaToDraw: CanvasImageSource;
          let naturalWidth: number, naturalHeight: number;

          if (visualMediaType === "image") {
            mediaToDraw = visualMedia as HTMLImageElement;
            naturalWidth = (visualMedia as HTMLImageElement).naturalWidth;
            naturalHeight = (visualMedia as HTMLImageElement).naturalHeight;
          } else { 
            const vid = visualMedia as HTMLVideoElement;
            if (vid.readyState >= HTMLMediaElement.HAVE_METADATA && vid.duration > 0) {
              const targetVideoTime = frameTime % vid.duration;
              // Only seek if significantly different to avoid performance issues from too frequent seeking
              if (Math.abs(vid.currentTime - targetVideoTime) > (1 / fps) + 0.05 ) { 
                  const seekPromise = new Promise<void>((resolveSeek, rejectSeek) => {
                      const onSeeked = () => {
                          vid.removeEventListener('seeked', onSeeked);
                          vid.removeEventListener('error', onSeekError);
                          resolveSeek();
                      };
                      const onSeekError = (ev: Event) => {
                          vid.removeEventListener('seeked', onSeeked);
                          vid.removeEventListener('error', onSeekError);
                          console.error('[Exporter] Video seek error during export:', (ev.target as HTMLVideoElement)?.error);
                          rejectSeek(new Error('Error seeking visual video frame.'));
                      };
                      vid.addEventListener('seeked', onSeeked, { once: true });
                      vid.addEventListener('error', onSeekError, { once: true });
                      vid.currentTime = targetVideoTime;
                  });
                  try {
                      await seekPromise;
                  } catch (seekError) {
                      // If seek fails, we'll try to draw the current frame, but it might be out of sync.
                      console.warn("[Exporter] Failed to seek video frame for export, using current frame:", seekError);
                  }
              }
            }
            mediaToDraw = vid;
            naturalWidth = vid.videoWidth;
            naturalHeight = vid.videoHeight;
          }
          
          if (naturalWidth > 0 && naturalHeight > 0) {
              const mediaAspectRatio = naturalWidth / naturalHeight;
              const canvasAspectRatio = outputWidth / outputHeight;
              let baseDrawWidth, baseDrawHeight;

              if (mediaAspectRatio > canvasAspectRatio) { 
                  baseDrawWidth = outputWidth;
                  baseDrawHeight = outputWidth / mediaAspectRatio;
              } else { 
                  baseDrawHeight = outputHeight;
                  baseDrawWidth = outputHeight * mediaAspectRatio;
              }
              const finalDrawWidth = baseDrawWidth * scale;
              const finalDrawHeight = baseDrawHeight * scale;
              const offsetX = (outputWidth - finalDrawWidth) / 2;
              const offsetY = (outputHeight - finalDrawHeight) / 2;
              ctx.drawImage(mediaToDraw, offsetX, offsetY, finalDrawWidth, finalDrawHeight);
          }
          
          await new Promise(requestAnimationFrame); 
        }
        console.log("[Exporter] Finished rendering all frames to canvas.");
      };

      await renderFrames();

      onProgress(0.95, "Finalizing video..."); 
      console.log(`[Exporter] All frames rendered. Attempting to stop recorder. Recorder state: ${recorder.state}. AudioContext currentTime: ${audioContext?.currentTime.toFixed(3)}`);
      
      // Delay stopping the audio source node until after the recorder has processed the last audio.
      // This is a heuristic. The 'ended' event on the AudioSourceNode is more reliable if it aligns with recorder.
      // However, MediaRecorder might need a bit more time for its internal buffers.
      // We expect recorder.stop() to trigger ondataavailable for final chunks, and then onstop.
      // AudioSourceNode.stop() can be called, but its 'onended' is mostly for knowing the source *itself* finished.
      // The key is combinedStream needs to be live for recorder.

      if (recorder && recorder.state === "recording") {
         console.log("[Exporter] Explicitly stopping MediaRecorder.");
         recorder.stop();
         // audioSourceNode.stop(); // Can be stopped slightly after, or rely on recorder to finish with audio stream
      } else if (recorder && recorder.state !== "inactive") {
        console.warn(`[Exporter] Recorder in unexpected state: ${recorder.state} before final stop. Attempting stop anyway.`);
        recorder.stop();
        // audioSourceNode.stop();
      } else if (recorder && recorder.state === "inactive") {
        console.log("[Exporter] Recorder was already inactive before explicit stop. This means onstop/onerror should have handled it or an error occurred earlier.");
        if (recordedChunks.length === 0 && audioDuration > 0) { 
            reject(new Error("Recording appears to have stopped prematurely with no data, but audio was present."));
            cleanup();
            return;
        }
      } else {
        console.error("[Exporter] CRITICAL: Recorder not available or already stopped when trying to finalize, and onstop may not have fired.");
        if (recordedChunks.length > 0) {
             console.log("[Exporter] Chunks exist, assuming onstop will handle resolution.");
        } else { 
            reject(new Error("Recorder not available or stopped with no data. Export failed."));
            cleanup(); 
            return;
        }
      }
      
      // It's important that audioSourceNode is not stopped *before* recorder.stop() if the recorder is still processing.
      // Typically, you stop the sources feeding the MediaRecorder *after* you've called recorder.stop()
      // and the onstop event has fired, or if the source naturally ends (like an AudioBufferSourceNode).
      // Since AudioBufferSourceNode will naturally end, we might not need to explicitly call stop on it here,
      // letting it play out fully. The MediaRecorder should capture this.
      // However, if cleanup is needed sooner, or to be very explicit:
      // setTimeout(() => {
      //    if(audioSourceNode && audioSourceNode.playbackState === audioSourceNode.PLAYING_STATE) audioSourceNode.stop();
      // }, 100); // Small delay

    } catch (error) {
      console.error("[Exporter] Export to MP4/WebM failed with error:", error);
      reject(error); 
      cleanup(); 
    }
  });
}
