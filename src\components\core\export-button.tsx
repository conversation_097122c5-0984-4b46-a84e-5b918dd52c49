"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Download, Loader2 } from "lucide-react";
import { useState, useRef } from "react";

// Import the improved server-side export function
import { exportWithImprovedServerFFmpeg } from "@/lib/video-exporter-server";

// Constants
const OUTPUT_WIDTH = 1280;
const OUTPUT_HEIGHT = 720;
const FPS = 30;

interface ExportButtonProps {
  songDataUri: string | null;
  visualMediaSrc: string | null;
  visualMediaType: "image" | "video" | null;
  selectedPreset?: string | null;
  pulseIntensity?: 'light' | 'medium' | 'heavy';
  glitchIntensity?: 'light' | 'medium' | 'heavy';
  canvasBackgroundColor?: string;
  backgroundImage?: string | null;
}

export default function ExportButton({
  songDataUri,
  visualMediaSrc,
  visualMediaType,
  selectedPreset = "pulse",
  pulseIntensity = "heavy",
  glitchIntensity = "heavy",
  canvasBackgroundColor = "#262626",
  backgroundImage = null
}: ExportButtonProps) {
  const [isExporting, setIsExporting] = useState(false);
  const { toast } = useToast();
  const activeToastRef = useRef<string | null>(null);

  const handleExport = async () => {
    if (!songDataUri || !visualMediaSrc || !visualMediaType) {
      toast({
        title: "Export Error",
        description: "Please upload both a song and a visual media (image or video).",
        variant: "destructive",
      });
      return;
    }

    if (isExporting) {
      return;
    }

    setIsExporting(true);

    // Create a toast notification that we'll update with progress
    activeToastRef.current = toast({
      title: "Preparing Export",
      description: "Initializing...",
      duration: 100000, // Long duration as we'll manually dismiss it
    }).id;

    try {
      // Use the improved server-side export method
      const result = await exportWithImprovedServerFFmpeg({
        songDataUri,
        visualMediaSrc,
        visualMediaType,
        outputWidth: OUTPUT_WIDTH,
        outputHeight: OUTPUT_HEIGHT,
        fps: FPS,
        selectedPreset,
        pulseIntensity,
        glitchIntensity,
        canvasBackgroundColor,
        backgroundImage,
        onProgress: (progress: number, info?: string) => {
          if (activeToastRef.current) {
            let descriptionText = info || `Progress: ${(progress * 100).toFixed(0)}%`;
            toast({
              id: activeToastRef.current,
              title: "Exporting Video",
              description: descriptionText,
            });
          }
        },
      });

      if (activeToastRef.current) {
        toast({
          id: activeToastRef.current,
          title: "Export Complete",
          description: "Your video has been successfully exported.",
          duration: 5000,
        });
        activeToastRef.current = null;
      }

      // Create a download link for the video
      const url = URL.createObjectURL(result.blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'rhythmic-canvas-video.mp4';
      document.body.appendChild(a);
      a.click();

      // Clean up
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 100);

    } catch (error) {
      console.error("Export failed:", error);

      if (activeToastRef.current) {
        toast({
          id: activeToastRef.current,
          title: "Export Failed",
          description: `An error occurred during export: ${error instanceof Error ? error.message : String(error)}`,
          variant: "destructive",
          duration: 5000,
        });
        activeToastRef.current = null;
      }
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      onClick={handleExport}
      disabled={isExporting || !songDataUri || !visualMediaSrc}
    >
      {isExporting ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Exporting...
        </>
      ) : (
        <>
          <Download className="mr-2 h-4 w-4" />
          Export Video
        </>
      )}
    </Button>
  );
}


