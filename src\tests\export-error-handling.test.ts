// export-error-handling.test.ts
// Test suite to validate error handling improvements

import { exportWithImprovedServerFFmpeg } from '../lib/video-exporter-server';

interface TestResult {
  success: boolean;
  error?: string;
  duration?: number;
}

class ExportErrorTester {
  
  async testBasicExport(): Promise<TestResult> {
    const startTime = Date.now();
    
    // Create a simple test audio data URI (1 second of silence)
    const testAudioUri = this.createTestAudioDataUri();
    
    // Create a simple test image data URI
    const testImageUri = this.createTestImageDataUri();
    
    const options = {
      songDataUri: testAudioUri,
      visualMediaSrc: testImageUri,
      visualMediaType: 'image' as const,
      outputWidth: 640,
      outputHeight: 480,
      fps: 30,
      selectedPreset: 'pulse',
      pulseIntensity: 'medium' as const,
      glitchIntensity: 'medium' as const,
      onProgress: (progress: number, info?: string) => {
        console.log(`Test Progress: ${(progress * 100).toFixed(1)}% - ${info || 'Processing...'}`);
      }
    };

    try {
      console.log('🧪 Starting basic export test...');
      const result = await exportWithImprovedServerFFmpeg(options);
      const duration = Date.now() - startTime;
      
      console.log('✅ Basic export test completed successfully');
      console.log(`📊 Export duration: ${duration}ms`);
      console.log(`📦 Result blob size: ${result.blob.size} bytes`);
      
      return {
        success: true,
        duration
      };
    } catch (error) {
      console.error('❌ Basic export test failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: Date.now() - startTime
      };
    }
  }

  async testErrorHandling(): Promise<TestResult[]> {
    const results: TestResult[] = [];
    
    // Test 1: Invalid audio URI
    console.log('🧪 Testing invalid audio URI handling...');
    try {
      await exportWithImprovedServerFFmpeg({
        songDataUri: 'invalid-uri',
        visualMediaSrc: this.createTestImageDataUri(),
        visualMediaType: 'image',
        outputWidth: 640,
        outputHeight: 480,
        fps: 30,
        selectedPreset: 'pulse'
      });
      results.push({ success: false, error: 'Should have failed with invalid audio URI' });
    } catch (error) {
      console.log('✅ Correctly handled invalid audio URI');
      results.push({ success: true });
    }

    // Test 2: Invalid image URI
    console.log('🧪 Testing invalid image URI handling...');
    try {
      await exportWithImprovedServerFFmpeg({
        songDataUri: this.createTestAudioDataUri(),
        visualMediaSrc: 'invalid-image-uri',
        visualMediaType: 'image',
        outputWidth: 640,
        outputHeight: 480,
        fps: 30,
        selectedPreset: 'pulse'
      });
      results.push({ success: false, error: 'Should have failed with invalid image URI' });
    } catch (error) {
      console.log('✅ Correctly handled invalid image URI');
      results.push({ success: true });
    }

    // Test 3: Invalid dimensions
    console.log('🧪 Testing invalid dimensions handling...');
    try {
      await exportWithImprovedServerFFmpeg({
        songDataUri: this.createTestAudioDataUri(),
        visualMediaSrc: this.createTestImageDataUri(),
        visualMediaType: 'image',
        outputWidth: 0,
        outputHeight: 0,
        fps: 30,
        selectedPreset: 'pulse'
      });
      results.push({ success: false, error: 'Should have failed with invalid dimensions' });
    } catch (error) {
      console.log('✅ Correctly handled invalid dimensions');
      results.push({ success: true });
    }

    return results;
  }

  private createTestAudioDataUri(): string {
    // Create a minimal WAV file data URI (1 second of silence at 44.1kHz)
    const sampleRate = 44100;
    const duration = 1; // 1 second
    const numSamples = sampleRate * duration;
    const numChannels = 1;
    const bytesPerSample = 2;
    
    const buffer = new ArrayBuffer(44 + numSamples * bytesPerSample);
    const view = new DataView(buffer);
    
    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + numSamples * bytesPerSample, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * bytesPerSample, true);
    view.setUint16(32, numChannels * bytesPerSample, true);
    view.setUint16(34, 8 * bytesPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, numSamples * bytesPerSample, true);
    
    // Silent audio data (all zeros)
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(44 + i * 2, 0, true);
    }
    
    const blob = new Blob([buffer], { type: 'audio/wav' });
    return URL.createObjectURL(blob);
  }

  private createTestImageDataUri(): string {
    // Create a simple 100x100 red square
    const canvas = document.createElement('canvas');
    canvas.width = 100;
    canvas.height = 100;
    const ctx = canvas.getContext('2d')!;
    
    // Fill with red color
    ctx.fillStyle = '#ff0000';
    ctx.fillRect(0, 0, 100, 100);
    
    // Add some visual interest
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(25, 25, 50, 50);
    
    return canvas.toDataURL('image/jpeg', 0.8);
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Export Error Handling Tests...\n');

    try {
      // Test 1: Basic Export
      console.log('📋 Test 1: Basic Export Functionality');
      const basicResult = await this.testBasicExport();
      if (basicResult.success) {
        console.log(`✅ Basic export test passed (${basicResult.duration}ms)\n`);
      } else {
        console.log(`❌ Basic export test failed: ${basicResult.error}\n`);
      }

      // Test 2: Error Handling
      console.log('📋 Test 2: Error Handling');
      const errorResults = await this.testErrorHandling();
      const passedErrorTests = errorResults.filter(r => r.success).length;
      console.log(`✅ Error handling tests: ${passedErrorTests}/${errorResults.length} passed\n`);

      // Summary
      console.log('📊 TEST SUMMARY:');
      console.log(`• Basic Export: ${basicResult.success ? 'PASS' : 'FAIL'}`);
      console.log(`• Error Handling: ${passedErrorTests}/${errorResults.length} PASS`);
      
      if (basicResult.success && passedErrorTests === errorResults.length) {
        console.log('🎉 All tests passed! Export system is working correctly.');
      } else {
        console.log('⚠️ Some tests failed. Please check the implementation.');
      }

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      throw error;
    }
  }
}

// Export for use in other test files
export { ExportErrorTester, TestResult };

// Run tests if this file is executed directly
if (typeof window !== 'undefined' && require.main === module) {
  const tester = new ExportErrorTester();
  tester.runAllTests().catch(console.error);
}
