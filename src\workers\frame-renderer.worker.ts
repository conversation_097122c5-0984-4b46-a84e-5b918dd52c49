// frame-renderer.worker.ts
// Web Worker for parallel frame rendering to improve video export performance

interface FrameRenderRequest {
  id: string;
  timeSeconds: number;
  width: number;
  height: number;
  visualMediaSrc: string;
  visualMediaType: 'image' | 'video';
  selectedPreset: string;
  pulseIntensity: 'light' | 'medium' | 'heavy';
  glitchIntensity: 'light' | 'medium' | 'heavy';
  audioBuffer?: ArrayBuffer;
  canvasBackgroundColor: string;
  backgroundImage?: string;
}

interface FrameRenderResponse {
  id: string;
  imageData?: ImageData;
  error?: string;
}

// Constants matching the main thread
const ANALYSIS_WINDOW_DURATION_SEC = 0.05;

const PULSE_LEVELS = {
  light: { maxScale: 0.1, rmsBoost: 2.0 },
  medium: { maxScale: 0.2, rmsBoost: 3.0 },
  heavy: { maxScale: 0.3, rmsBoost: 4.0 }
} as const;

const GLITCH_LEVELS = {
  light: {
    maxDisplacement: 5,
    noiseIntensity: 0.02,
    channelSeparation: 2,
    compressionBlocks: 3,
    scanLineIntensity: 0.1,
    rmsBoost: 2.0
  },
  medium: {
    maxDisplacement: 10,
    noiseIntensity: 0.05,
    channelSeparation: 4,
    compressionBlocks: 6,
    scanLineIntensity: 0.2,
    rmsBoost: 3.0
  },
  heavy: {
    maxDisplacement: 20,
    noiseIntensity: 0.1,
    channelSeparation: 8,
    compressionBlocks: 10,
    scanLineIntensity: 0.3,
    rmsBoost: 4.0
  }
} as const;

class FrameRenderer {
  private canvas: OffscreenCanvas;
  private ctx: OffscreenCanvasRenderingContext2D;
  private audioContext: AudioContext | null = null;
  private audioBuffer: AudioBuffer | null = null;
  private visualElement: HTMLImageElement | HTMLVideoElement | null = null;

  constructor() {
    this.canvas = new OffscreenCanvas(1280, 720);
    this.ctx = this.canvas.getContext('2d')!;
  }

  async initialize(request: FrameRenderRequest): Promise<void> {
    // Set canvas size
    this.canvas.width = request.width;
    this.canvas.height = request.height;

    // Initialize audio context and buffer if needed
    if ((request.selectedPreset === 'pulse' || request.selectedPreset === 'glitch') && request.audioBuffer) {
      try {
        this.audioContext = new AudioContext();
        this.audioBuffer = await this.audioContext.decodeAudioData(request.audioBuffer.slice(0));
      } catch (error) {
        console.error('Failed to decode audio buffer in worker:', error);
      }
    }

    // Load visual media
    if (request.visualMediaSrc) {
      if (request.visualMediaType === 'image') {
        this.visualElement = new Image();
        this.visualElement.crossOrigin = 'anonymous';
        await new Promise((resolve, reject) => {
          this.visualElement!.onload = resolve;
          this.visualElement!.onerror = reject;
          (this.visualElement as HTMLImageElement).src = request.visualMediaSrc;
        });
      } else if (request.visualMediaType === 'video') {
        // Note: Video handling in workers is limited, we'll handle this differently
        console.warn('Video rendering in worker is limited, falling back to main thread');
      }
    }
  }

  calculateAudioEnergyAtTime(currentTime: number, intensityLevel: 'light' | 'medium' | 'heavy', preset: string): number {
    if (!this.audioBuffer || !this.audioContext) return 0;

    const sampleRate = this.audioBuffer.sampleRate;
    const windowSamples = Math.floor(ANALYSIS_WINDOW_DURATION_SEC * sampleRate);

    let startSample = Math.floor(currentTime * sampleRate);
    startSample = Math.max(0, startSample);

    let endSample = startSample + windowSamples;
    endSample = Math.min(endSample, this.audioBuffer.length);
    startSample = Math.min(startSample, endSample);

    if (startSample >= this.audioBuffer.length || startSample >= endSample) return 0;

    const channelData = this.audioBuffer.getChannelData(0);
    const segment = channelData.slice(startSample, endSample);

    if (segment.length === 0) return 0;

    let sumOfSquares = 0;
    for (let i = 0; i < segment.length; i++) {
      sumOfSquares += segment[i] * segment[i];
    }
    const rms = Math.sqrt(sumOfSquares / segment.length);

    const rmsBoost = preset === 'pulse'
      ? PULSE_LEVELS[intensityLevel].rmsBoost
      : GLITCH_LEVELS[intensityLevel].rmsBoost;

    return Math.min(rms * rmsBoost, 1.0);
  }

  async renderFrame(request: FrameRenderRequest): Promise<ImageData> {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Set background
    if (request.canvasBackgroundColor) {
      this.ctx.fillStyle = request.canvasBackgroundColor;
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // Draw background image if present
    if (request.backgroundImage && this.visualElement) {
      // Background image handling would need to be implemented
    }

    if (!this.visualElement) {
      return this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    }

    // Calculate effects based on audio
    let scale = 1.0;
    let glitchParams = {
      displacement: 0,
      channelSeparation: 0,
      noiseLevel: 0,
      compressionLevel: 0,
      scanLinePosition: 0,
      scanLineIntensity: 0
    };

    if (request.selectedPreset === 'pulse') {
      const audioEnergy = this.calculateAudioEnergyAtTime(request.timeSeconds, request.pulseIntensity, 'pulse');
      scale = 1.0 + (audioEnergy * PULSE_LEVELS[request.pulseIntensity].maxScale);
    } else if (request.selectedPreset === 'glitch') {
      const audioEnergy = this.calculateAudioEnergyAtTime(request.timeSeconds, request.glitchIntensity, 'glitch');
      const glitchLevel = GLITCH_LEVELS[request.glitchIntensity];

      glitchParams = {
        displacement: audioEnergy * glitchLevel.maxDisplacement * (Math.random() - 0.5) * 2,
        channelSeparation: audioEnergy * glitchLevel.channelSeparation,
        noiseLevel: audioEnergy * glitchLevel.noiseIntensity,
        compressionLevel: audioEnergy * glitchLevel.compressionBlocks,
        scanLinePosition: Math.random(),
        scanLineIntensity: audioEnergy * glitchLevel.scanLineIntensity
      };
    }

    // Apply transformations
    this.ctx.save();
    
    if (request.selectedPreset === 'pulse') {
      // Center the scaling
      this.ctx.translate(this.canvas.width / 2, this.canvas.height / 2);
      this.ctx.scale(scale, scale);
      this.ctx.translate(-this.canvas.width / 2, -this.canvas.height / 2);
    } else if (request.selectedPreset === 'glitch') {
      this.ctx.translate(glitchParams.displacement, 0);
    }

    // Draw the visual element
    if (this.visualElement instanceof HTMLImageElement) {
      this.ctx.drawImage(this.visualElement, 0, 0, this.canvas.width, this.canvas.height);
    }

    this.ctx.restore();

    // Apply glitch effects if needed
    if (request.selectedPreset === 'glitch') {
      this.applyGlitchEffects(glitchParams);
    }

    return this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
  }

  private applyGlitchEffects(glitchParams: any): void {
    // Get current image data
    const imageData = this.ctx.getImageData(0, 0, this.canvas.width, this.canvas.height);
    const data = imageData.data;

    // Apply channel separation
    if (glitchParams.channelSeparation > 0) {
      const separation = Math.floor(glitchParams.channelSeparation);
      for (let i = 0; i < data.length; i += 4) {
        const srcIndex = Math.max(0, i - separation * 4);
        if (srcIndex < data.length) {
          data[i] = data[srcIndex]; // Red channel shift
        }
      }
    }

    // Apply noise
    if (glitchParams.noiseLevel > 0) {
      for (let i = 0; i < data.length; i += 4) {
        if (Math.random() < glitchParams.noiseLevel) {
          data[i] = Math.random() * 255;     // R
          data[i + 1] = Math.random() * 255; // G
          data[i + 2] = Math.random() * 255; // B
        }
      }
    }

    // Apply scan lines
    if (glitchParams.scanLineIntensity > 0) {
      const scanLineY = Math.floor(glitchParams.scanLinePosition * this.canvas.height);
      const lineWidth = Math.max(1, Math.floor(glitchParams.scanLineIntensity * 10));
      
      for (let y = scanLineY; y < Math.min(scanLineY + lineWidth, this.canvas.height); y++) {
        for (let x = 0; x < this.canvas.width; x++) {
          const index = (y * this.canvas.width + x) * 4;
          if (index < data.length) {
            data[index] = Math.min(255, data[index] + glitchParams.scanLineIntensity * 100);     // R
            data[index + 1] = Math.min(255, data[index + 1] + glitchParams.scanLineIntensity * 100); // G
            data[index + 2] = Math.min(255, data[index + 2] + glitchParams.scanLineIntensity * 100); // B
          }
        }
      }
    }

    // Put the modified image data back
    this.ctx.putImageData(imageData, 0, 0);
  }

  cleanup(): void {
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.audioBuffer = null;
    this.visualElement = null;
  }
}

// Worker message handling
const frameRenderer = new FrameRenderer();

self.onmessage = async (event: MessageEvent<FrameRenderRequest>) => {
  const request = event.data;
  
  try {
    await frameRenderer.initialize(request);
    const imageData = await frameRenderer.renderFrame(request);
    
    const response: FrameRenderResponse = {
      id: request.id,
      imageData
    };
    
    self.postMessage(response);
  } catch (error) {
    const response: FrameRenderResponse = {
      id: request.id,
      error: error instanceof Error ? error.message : String(error)
    };
    
    self.postMessage(response);
  }
};

export {};
